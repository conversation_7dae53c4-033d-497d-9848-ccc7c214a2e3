﻿using System.Collections;
using UnityEngine;
using UnityEngine.UI;
using DG.Tweening;

public class AlphaLoading : MonoBehaviour {
	public int maxTimeDestroy = 999999;//When loading time is too long. Maybe some bug. Destroy this view.
	private static AlphaLoading mInstance;

	[SerializeField]
	Text loadingText;

	[SerializeField]
	Image bg;

	/// <summary>
	/// 表示ステータス(0～100)
	/// </summary>
	int loadStatus = 0;
	/// <summary>
	/// 表示単位
	/// </summary>
	string unit = "%";

	/// <summary>
	/// loadingステータスのランダム加算値
	/// </summary>
	int[] randAddStatusAry = { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 2, 3, 4, 5 };

	float timeLoading;
	bool isCustom = false;

	public static AlphaLoading Instance {
		get {
			// 無ければ作成
			if (AlphaLoading.mInstance == null) {
				GameObject obj = UnityEngine.Object.Instantiate(Resources.Load("AlphaLoadingCanvas")) as GameObject;
				DontDestroyOnLoad(obj);
				AlphaLoading.mInstance = obj.GetComponent<AlphaLoading>();
			}
			return AlphaLoading.mInstance;
		}
	}

	private void Awake() {
		if (AlphaLoading.mInstance == null) {
			AlphaLoading.mInstance = this;
		} else {
			Destroy(this.gameObject);
		}
	}

	/// <summary>
	/// 初回処理
	/// </summary>
	void Start() {
		Init();
	}

	/// <summary>
	/// 初期化
	/// </summary>
	void Init() {
		loadingText.text = loadStatus + unit;
		timeLoading = 0;
		bg.DOFade(0,0);
	}

	/// <summary>
	/// 終了(破棄)
	/// </summary>
	void End(float loadingTime = 0.5f) {
		bg.DOFade(0, loadingTime);
		loadStatus = 100;
		// 数コンマ秒待ってから破棄
		StartCoroutine(WaitDestroy(loadingTime));
		if(Time.timeScale == 0) Destroy(this.gameObject);
	}

	/// <summary>
	/// 1秒待って破棄
	/// </summary>
	/// <returns></returns>
	IEnumerator WaitDestroy(float loadingTime = 0.5f) {
		yield return new WaitForSeconds(loadingTime);
		Destroy(this.gameObject);
	}

	/// <summary>
	///
	/// </summary>
	public void StartLoading(float loadingTime = 0.5f) {
		Init();
		bg.DOFade(1, loadingTime);
	}

	/// <summary>
	/// 外部からの実行用終了通知
	/// 各シーンStart時かAPI通信終了時に実行する必要有。
	/// </summary>
	public void EndLoading(float loadingTime = 0.5f) {
		End(loadingTime);
	}

	/// <summary>
	///  更新処理
	/// </summary>
	void Update() {
		StatusUpdate();
	}
	/// <summary>
	/// loading数値更新
	/// </summary>
	void StatusUpdate() {
		// 99までは進行させる
		if (loadStatus < 99) {
			int randAdd = randAddStatusAry[UnityEngine.Random.Range(0, randAddStatusAry.Length)];
			loadStatus += randAdd;
			if (loadStatus > 99) {
				loadStatus = 99;
			}
		}
		if(!isCustom) loadingText.text = loadStatus + unit;
		timeLoading += Time.deltaTime;
        if(timeLoading > maxTimeDestroy)
        {
			EndLoading();
        }
	}

	public void ShowCustomLoadingText(string text){
		isCustom = true;
		loadingText.text = text;
	}
}

