﻿using System.Collections;
using System.Collections.Generic;

using UnityEngine;
using UnityEngine.UI;
using System;

public class SceneLoading : MonoBehaviour {
	private static SceneLoading mInstance;

	[SerializeField]
	Text loadingText;
	[SerializeField]
	GameObject bg;
	[SerializeField]
	GameObject content;

	[SerializeField] UnityEngine.Events.UnityEvent onHide;
	[SerializeField] float animTime = 0.2f;

	[SerializeField] Image loadingImage;
	[SerializeField] Sprite[] loadingSprites;

	public GameObject btnSkip;
	public Action OnSkip;
	private bool autoHideWhenSkip = true;

	private int loadStatus = 0;
	private float lastLoadStatusChangeTime = 0f;
	private float skipButtonShowDelay = 10f;
	private bool isSkipButtonShown = false;

    private string unit = "%";

    private int[] randAddStatusAry = { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 2, 3, 4, 5};

    bool isCustom = false;
	Font defaultFont;

    public static SceneLoading Instance {
		get {
			// 無ければ作成
			if (SceneLoading.mInstance == null) {
				GameObject obj = UnityEngine.Object.Instantiate(Resources.Load("SceneLoadingCanvas")) as GameObject;
				DontDestroyOnLoad(obj);
				SceneLoading.mInstance = obj.GetComponent<SceneLoading>();
			}
			return SceneLoading.mInstance;
		}
	}

	private void Awake() {
		if (SceneLoading.mInstance == null) {
			SceneLoading.mInstance = this;
		}
		defaultFont = loadingText.font;
	}

	/// <summary>
	/// 初回処理
	/// </summary>
	void Start () {
		Init();
	}

	/// <summary>
	/// 初期化
	/// </summary>
	void Init() {
		loadingText.text = loadStatus + unit;
	}

	/// <summary>
	/// 終了(破棄)
	/// </summary>
	void End() {
		loadStatus = 100;
		// 数コンマ秒待ってから破棄
		StartCoroutine(WaitHide());
	}

	/// <summary>
	/// 数秒待ちからの隠し
	/// </summary>
	/// <returns></returns>
	IEnumerator WaitHide() {
		if(onHide != null){
            onHide.Invoke();
			Debug.Log("On hide sceneloading");
		}
		yield return new WaitForSeconds(animTime);
		Hide();
	}

	/// <summary>
	/// 隠し
	/// </summary>
	void Hide() {
		bg.SetActive(false);
		content.SetActive(false);
        if(btnSkip != null) btnSkip.SetActive(false);
	}
	/// <summary>
	/// 表示
	/// </summary>
	void Show() {
		bg.SetActive(true);
		content.SetActive(true);
	}

	/// <summary>
	///
	/// </summary>
	public void StartLoading() {
        loadStatus = 0;
        Init ();
        Show ();
		if(loadingImage != null && loadingSprites.Length > 0){
			loadingImage.sprite = loadingSprites[UnityEngine.Random.Range(0, loadingSprites.Length - 1)];
		}

		// Reset skip button state
		lastLoadStatusChangeTime = Time.time;
		isSkipButtonShown = false;
		if(btnSkip != null) btnSkip.SetActive(false);
	}

	/// <summary>
	/// 外部からの実行用終了通知
	/// 各シーンStart時かAPI通信終了時に実行する必要有。
	/// </summary>
	public void EndLoading() {
		End();
	}

	/// <summary>
	///  更新処理
	/// </summary>
	void Update () {
		StatusUpdate();

        if (!isSkipButtonShown && Time.time - lastLoadStatusChangeTime >= skipButtonShowDelay)
        {
            ShowSkipButton();
        }
	}
	/// <summary>
	/// loading数値更新
	/// </summary>
	void StatusUpdate() {
		// 99までは進行させる
		if (loadStatus < 99) {
			int prevLoadStatus = loadStatus;
			int randAdd = randAddStatusAry[UnityEngine.Random.Range(0,randAddStatusAry.Length)];
			loadStatus += randAdd;
			if (loadStatus > 99) {
				loadStatus = 99;
			}
		}
        if (!isCustom) loadingText.text = loadStatus + unit;
	}

	private void ShowSkipButton() {
		if (btnSkip != null && !btnSkip.activeSelf) {
			DebugLogger.Log("Showing skip button after " + skipButtonShowDelay + " seconds of stalled loading");
			btnSkip.SetActive(true);
			isSkipButtonShown = true;

			// Set the button text using TextTranslator
			Text skipButtonText = btnSkip.GetComponentInChildren<Text>();
			if (skipButtonText != null) {
				skipButtonText.text = TextTranslator.Instance.GetText("CANCEL");
				skipButtonText.font = TextTranslator.Instance.GetFontByCurrentLanguage(defaultFont);
			}

			// Set the default skip action if none is provided
			if (OnSkip == null) {
				OnSkip = () => {
					DebugLogger.Log("Skip button clicked, ending loading");
					EndLoading();
				};
			}
		}
	}


    public void ShowCustomLoadingText(string text)
    {
        isCustom = true;
        loadingText.text = text;
        loadingText.font = TextTranslator.Instance.GetFontByCurrentLanguage(defaultFont);
    }

	public void ShowCustomLoadingTextWithSkipButton(string text, Action onSkip, bool autoHideWhenSkip = true)
	{
		ShowCustomLoadingText(text);
		if(btnSkip != null) btnSkip.SetActive(true);
		OnSkip = onSkip;
		this.autoHideWhenSkip = autoHideWhenSkip;
	}

	public void OnClickSkip()
	{
		if (OnSkip != null)
		{
			OnSkip();
		}
		if (this.autoHideWhenSkip) EndLoading();
	}
}
