﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

[RequireComponent(typeof(Button))]
[DisallowMultipleComponent]
public class ButtonSound : MonoBehaviour
{
    public enum ButtonType
    {
        se_bt,
        se_bt_ok,
        se_bt_cancel
    }

    public ButtonType buttonType;
    private Button button;

    public void Start()
    {
        button = GetComponent<Button>();
        button.onClick.AddListener(OnButtonClick);
    }

    protected virtual void OnButtonClick()
    {
        AudioManager2.Instance.PlaySFX(buttonType.ToString());
    }
}
