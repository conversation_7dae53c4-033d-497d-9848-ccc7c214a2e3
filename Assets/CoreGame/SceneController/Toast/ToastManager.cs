﻿using UnityEngine;
using System.Collections;
using System;

public class ToastManager : Singleton<ToastManager>, IToastManager {

	// Use this for initialization
	private PoolGameObject pools;
	private GameObject toastHolder;

	void Awake() {
		pools = new PoolGameObject ();
	}

	public void showToast(string info, bool useLastToast = false, Action callbackDone = null){
        if (toastHolder == null) {
            toastHolder = Instantiate(Resources.Load ("Prefabs/ToastHolder") as GameObject) as GameObject;
			DontDestroyOnLoad(toastHolder);
        }
		GameObject instance = pools.getInCache();
		if(instance == null) {
			instance = createFirstObject();
			pools.cacheNewAndUse(instance);
		}
        instance.SetActive (true);
        instance.transform.SetParent (toastHolder.transform);
        instance.transform.localScale = new Vector3 (1, 1, 1);
        instance.transform.position = Vector3.zero;
		Toast toast = instance.GetComponent<Toast> ();
		toast.showToast (this, info, callbackDone);
	}

	public void backToCache(GameObject g) {
		pools.backToCache (g);
	}

	private GameObject createFirstObject () {
		GameObject g = Instantiate (Resources.Load ("Toast", typeof(GameObject))) as GameObject;
		return g;
	}
}
