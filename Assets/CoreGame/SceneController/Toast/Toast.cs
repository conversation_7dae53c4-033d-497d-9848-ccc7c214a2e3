﻿using UnityEngine;
using System.Collections;
using UnityEngine.UI;
using Beebyte.Obfuscator;
using DG.Tweening;
using System;

public class Toast : MonoBehaviour {
	Text text;
	RectTransform rect;
	public string infoText;
    public float startPosition = -400;
	private IToastManager _iToastManager;
	private Action callbackDone;

    [SkipRename]
	void OnComplete(){
		gameObject.SetActive (false);
		_iToastManager.backToCache (gameObject);
		if (callbackDone != null)
		{
			callbackDone();
		}
	}

	public void showToast(IToastManager _iToastManager, string info, Action callbackDone = null) {
		this.callbackDone = callbackDone;
		this._iToastManager = _iToastManager;
		infoText = info;
		text = GetComponent<Text> ();
        rect = GetComponent<RectTransform>();
        if (text == null)
        {
            text = GetComponentInChildren<Text>();
        }

        rect.anchoredPosition = new Vector2 (0, startPosition);
		text.text = info;
        DOVirtual.DelayedCall(Time.fixedDeltaTime * 2, () =>
        {
            rect.sizeDelta = new Vector2(text.rectTransform.sizeDelta.x + 10, text.rectTransform.sizeDelta.y + 10);
        });
        transform.SetAsLastSibling();
        
        DOTween.Kill (gameObject);
        rect.DOAnchorPosY (rect.anchoredPosition.y + 50, 2.5f).SetEase (Ease.OutQuad).OnComplete (OnComplete).SetId(gameObject);
	}
}
