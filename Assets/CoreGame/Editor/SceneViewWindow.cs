﻿using System.IO;
using UnityEditor;
using UnityEngine;
using System.Linq;

/// <summary>
/// Taken from http://wiki.unity3d.com/index.php/SceneViewWindow
/// </summary>
using System.Collections.Generic;
using UnityEditor.SceneManagement;


namespace UBootstrap.Editor
{
    public class SceneViewWindow : EditorWindow
    {
        /// <summary>
        /// Tracks scroll position.
        /// </summary>
        private Vector2 scrollPos;

        private static List<string> listScene = new List<string>();

        /// <summary>
        /// Initialize window state.
        /// </summary>
        [MenuItem ("Tools/Scene View")]
        internal static void Init ()
        {
            // EditorWindow.GetWindow() will return the open instance of the specified window or create a new
            // instance if it can't find one. The second parameter is a flag for creating the window as a
            // Utility window; Utility windows cannot be docked like the Scene and Game view windows.
            var window = (SceneViewWindow)GetWindow (typeof(SceneViewWindow), false, "Scene View");
            window.position = new Rect (window.position.xMin + 100f, window.position.yMin + 100f, 200f, 400f);
        }

        /// <summary>
        /// Called on GUI events.
        /// </summary>
        internal void OnGUI ()
        {
            EditorGUILayout.BeginVertical ();
            this.scrollPos = EditorGUILayout.BeginScrollView (this.scrollPos, false, false);

            GUILayout.Label ("Scenes In Build", EditorStyles.boldLabel);
            if (listScene.Count == 0 || listScene.Count != EditorBuildSettings.scenes.Length) {
                listScene.Clear ();
                foreach (var item in EditorBuildSettings.scenes) {
                    listScene.Add (Path.GetFileNameWithoutExtension (item.path));
                }
                listScene.Sort ();
            }
            for (var i = 0; i < listScene.Count; i++) {
                var sceneName = listScene [i];
                var scene = EditorBuildSettings.scenes.First ((EditorBuildSettingsScene arg) => {
                    return Path.GetFileNameWithoutExtension (arg.path) == sceneName;
                });
                var pressed = GUILayout.Button (i + ": " + sceneName, new GUIStyle (GUI.skin.GetStyle ("Button")) { alignment = TextAnchor.MiddleLeft });
                if (pressed) {
                    if (EditorSceneManager.SaveCurrentModifiedScenesIfUserWantsTo ()) {
                        EditorSceneManager.OpenScene (scene.path);
                    }
                }

            }

            EditorGUILayout.EndScrollView ();
            EditorGUILayout.EndVertical ();
        }
    }

}