﻿using UnityEditor;
using UnityEngine;
using System;
using System.IO;

public class PlayerPrefsEditor {

    [MenuItem("Tools/PlayerPrefs/DeleteAll")]
    static void DeleteAll()
    {
        PlayerPrefs.DeleteAll();
        Debug.Log("Delete All Data Of PlayerPrefs!!");
		foreach (var file in Directory.GetFiles(Application.persistentDataPath))
        {
            if (file.Contains(".dat") || file.Contains(".json")) {
                FileInfo file_info = new FileInfo(file);
                file_info.Delete();
            }
        }
    }
}
