﻿using UnityEngine;
using Beebyte.Obfuscator;
#if ENABLE_VOXELBUSTERS_ESSENTIAL_KIT
using VoxelBusters.EssentialKit.Editor;
using VoxelBusters.EssentialKit;
using VoxelBusters.CoreLibrary.NativePlugins;
using VoxelBusters.CoreLibrary;
#endif
#if UNITY_EDITOR
using UnityEditor;
#endif
[CreateAssetMenu(fileName = "BuildConfig", menuName = "Data/BuildConfig", order = 1)]
public class BuildConfig : ScriptableObject {
    public string APP_ID = "com.monkeypiano.pianotiles.perfect";
    // We change version when some update come and need update to old game
    public string GAME_VERSION       = "1.0";
    public int    GAME_VERSION_NUM   = 1;
    public string GAME_NAME          = "Piano Tiles";

    public string keystorePath = "game_common.keystore";
    public string keystorePass = "springsun";
    public string keyaliasName = "sunny";
    public string keyaliasPass = "springsun";
    public string DEFINE = "SUN MONKEY";
    public string COMPANY_NAME = "Monkey King Co.,Ltd";
    public Options optionsObsfucator;
    public bool isBuild = true;
    public bool isDeleteStreammmingAssets = false;
    public AdsData adsDataAndroid;
    public AdsData adsDataIos;

    #if UNITY_EDITOR
    public void OnValidate()
    {
        Debug.Log("App id change to " + APP_ID);
        UpdateAppId(APP_ID);
    }

    public void UpdateAppId(string id)
    {
        #if ENABLE_VOXELBUSTERS_ESSENTIAL_KIT
        var settings = EssentialKitSettingsEditorUtility.DefaultSettings;
        RuntimePlatformConstantSet url = new RuntimePlatformConstantSet(id, null, id);
        settings.ApplicationSettings = new ApplicationSettings(url, settings.ApplicationSettings.RateMyAppSettings, settings.ApplicationSettings.UsagePermissionSettings);

        EditorUtility.SetDirty(settings);
        AssetDatabase.SaveAssets();
        #endif
    }
    #endif
}
