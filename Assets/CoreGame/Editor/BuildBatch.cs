using UnityEngine;
using UnityEditor;
using UnityEditor.SceneManagement;
using System.Collections;
using System.Linq;
using System.IO;

public class BuildBatch : MonoBehaviour {
    private static BuildConfig data;
    private static string[] scene = GetScenes();
    private static string assetBundleAndroidPath = "AssetBundles/Android/";
    private static string streammingAssetPath = "Assets/StreamingAssets/";
    private static string songDataDir = "Assets/Resources/SongData.asset";
    private static string beeAndroidDir = "Library/Bee/Android/";
    private static SongData songData;

	private static string[] GetScenes() {
		ArrayList levels = new ArrayList();
		foreach (EditorBuildSettingsScene scene in EditorBuildSettings.scenes) {
			if (scene.enabled) {
				levels.Add(scene.path);
			}
		}
		return (string[]) levels.ToArray(typeof(string));
	}

    private static void LoadConfig(){
        data = Resources.Load<BuildConfig> ("BuildConfig");
        songData = AssetDatabase.LoadAssetAtPath<SongData>(songDataDir);
    }

    private static void MoveBundleToStreammingAsset()
    {
        Debug.Assert(songData != null);
        var listWeek = songData.listSong.GroupBy(x => x.weekId).Select(x => x.First());
        foreach(var weekSong in listWeek)
        {
            if(!weekSong.isIncludeInBuild)
            {
                CopyFile(assetBundleAndroidPath + "/" + weekSong.weekId, streammingAssetPath + "/" + weekSong.weekId);
                CopyFile(assetBundleAndroidPath + "/" + weekSong.weekId + ".manifest", streammingAssetPath + "/" + weekSong.weekId + ".manifest");
            }
        }
    }

    public static void CopyFile(string sourceFile, string destinationFile)
    {
        if (File.Exists(sourceFile))
        {
            File.Copy(sourceFile, destinationFile, true);
        }
        else
        {
            Debug.LogError("Source file does not exist: " + sourceFile);
        }
    }

    private static void DeleteAllStreammingAssets()
    {
        if(!data.isDeleteStreammmingAssets) return;
        if (Directory.Exists(streammingAssetPath))
        {
            Directory.Delete(streammingAssetPath, true);
        }
        else
        {
            Debug.LogError("Folder does not exist: " + streammingAssetPath);
        }
    }

    private static void DeleteBeeAndroidFolder()
    {
        if (Directory.Exists(beeAndroidDir))
        {
            Directory.Delete(beeAndroidDir, true);
        }
        else
        {
            Debug.LogError("Folder does not exist: " + beeAndroidDir);
        }
    }

    private static bool CheckSongDataError()
    {
        foreach (var song in songData.listSong)
        {
            var errorSongs = songData.listSong.FindAll( s => s.weekId == song.weekId && s.isIncludeInBuild != song.isIncludeInBuild);
            if(errorSongs.Count > 0)
            {
                Debug.LogError("Song data error week id (isIncludeInBuild khac nhau giua cac bai): " + errorSongs[0].weekId + " song id : " + errorSongs[0].id);
                return true;
            }
        }
        //Check if two song exist with the same id
        var listSong = songData.listSong.GroupBy(x => x.id).Select(x => x.Count() > 1 ? x.First() : null);
        foreach(var song in listSong)
        {
            if(song != null)
            {
                Debug.LogError("Song data error id bi lap lai: " + song.id + " song id : " + song.id + " kiem tra xem co bi trung lap map giua 2 mod khong?");
                return true;
            }
        }
        
        var serverError = songData.GetSongDataServerError();
        if(serverError.Contains("not have download link"))
        {
            Debug.LogError("Song data error : " + serverError);
            return true;
        }
        return false;
    }

    private static bool UpdatePlayAssetDeliveryAndCheckHasError()
    {
        // Debug.Assert(songData != null);
        // var _assetDeliveryConfig = AssetDeliveryConfigSerializer.LoadConfig();
        // _assetDeliveryConfig.Refresh();
        // var listWeekMissing = songData.listSong.GroupBy(x => x.weekId).Select(x => x.First()).ToList().FindAll(x => !x.isIncludeInBuild && !x.NeedToDownloadAsset && !_assetDeliveryConfig.AssetBundlePacks.ContainsKey(x.weekId));
        // if(listWeekMissing.Count > 0)
        // {
        //     listWeekMissing.ForEach( x => Debug.LogError("Thieu asset bundle : " + x.weekId));
        //     return true;
        // }

        // foreach (var pack in _assetDeliveryConfig.AssetBundlePacks)
        // {
        //     var songWeek = songData.listSong.Find( s => s.weekId == pack.Value.Name);
        //     if(songWeek == null) continue;
        //     Debug.Log("Pack " + pack.Value.Name + " deliveryMode : " + pack.Value.DeliveryMode.ToString() +" songweek id : " + songWeek.id);
        //     pack.Value.DeliveryMode = songWeek.isIncludeInBuild ? Google.Android.AppBundle.Editor.AssetPackDeliveryMode.DoNotPackage : songWeek.isFastFollow ? Google.Android.AppBundle.Editor.AssetPackDeliveryMode.FastFollow : songWeek.isInstallTime ? Google.Android.AppBundle.Editor.AssetPackDeliveryMode.InstallTime : Google.Android.AppBundle.Editor.AssetPackDeliveryMode.OnDemand;
        // }
        // AssetDeliveryConfigSerializer.SaveConfig(_assetDeliveryConfig);
        return false;
    }

	// リリースビルド.
	[MenuItem("Window/Batch Build/Release Build (iOS + Android)")]
	public static void ReleaseBuild(){
        LoadConfig ();
        string symIOS = PlayerSettings.GetScriptingDefineSymbolsForGroup(BuildTargetGroup.iOS);
        string symAnd = PlayerSettings.GetScriptingDefineSymbolsForGroup(BuildTargetGroup.Android);
		string idIOS = PlayerSettings.applicationIdentifier;
		string idAnd = PlayerSettings.applicationIdentifier;
        string cn = PlayerSettings.companyName;

        // PlayerSettings.SetScriptingDefineSymbolsForGroup(BuildTargetGroup.iOS, data.DEFINE);
        // PlayerSettings.SetScriptingDefineSymbolsForGroup(BuildTargetGroup.Android, data.DEFINE);
        PlayerSettings.applicationIdentifier =  data.APP_ID;
        PlayerSettings.companyName = data.COMPANY_NAME;
        EditorSettings.spritePackerMode = SpritePackerMode.SpriteAtlasV2;
        // アイコン設定
//        SetIcon(false);
//        SetIcon(false);
        bool error = false;
        if (!error && !BuildiOS(true)) error = true;
        if (!error && !BuildAndroid(true)) error = true;

        // PlayerSettings.SetScriptingDefineSymbolsForGroup(BuildTargetGroup.iOS, symIOS);
        // PlayerSettings.SetScriptingDefineSymbolsForGroup(BuildTargetGroup.Android, symAnd);
		PlayerSettings.applicationIdentifier =  idAnd;
        PlayerSettings.companyName = cn;
        EditorSettings.spritePackerMode = SpritePackerMode.Disabled;

        EditorSceneManager.SaveOpenScenes();
//        if (error) EditorApplication.Exit(1);
	}
	[MenuItem("Window/Batch Build/Release Build (iOS)")]
	public static void ReleaseBuildIOS(){
        LoadConfig ();
        string symIOS = PlayerSettings.GetScriptingDefineSymbolsForGroup(BuildTargetGroup.iOS);
        string cn = PlayerSettings.companyName;
        PlayerSettings.applicationIdentifier =  data.APP_ID;
        PlayerSettings.bundleVersion = data.GAME_VERSION;
        PlayerSettings.iOS.buildNumber = data.GAME_VERSION_NUM +"";
        data.optionsObsfucator.enabled = true;
        EditorSettings.spritePackerMode = SpritePackerMode.SpriteAtlasV2;

        while (symIOS.IndexOf(";TEST_GAME") >= 0)
        {
            symIOS = symIOS.Replace(";TEST_GAME", "");
            Debug.Log("REMOVE TEST_GAME symbol symIOS " + symIOS);
        }
        PlayerSettings.SetScriptingDefineSymbolsForGroup(BuildTargetGroup.iOS, symIOS);
        PlayerSettings.companyName = data.COMPANY_NAME;
        // アイコン設定
//        SetIcon(false);
        EditorSceneManager.SaveOpenScenes();
        AssetDatabase.SaveAssets ();
        bool error = false;
        if (!error && !BuildiOS(true)) error = true;

        // PlayerSettings.SetScriptingDefineSymbolsForGroup(BuildTargetGroup.iOS, symIOS);
        PlayerSettings.companyName = cn;
        EditorSettings.spritePackerMode = SpritePackerMode.Disabled;

//        if (error) EditorApplication.Exit(1);
	}
	
    [MenuItem("Window/Batch Build/Test Build (iOS)")]
	public static void TestBuildIOS(){
        LoadConfig ();
        string symIOS = PlayerSettings.GetScriptingDefineSymbolsForGroup(BuildTargetGroup.iOS);
        string cn = PlayerSettings.companyName;
        PlayerSettings.applicationIdentifier =  data.APP_ID;
        PlayerSettings.bundleVersion = data.GAME_VERSION;
        PlayerSettings.iOS.buildNumber = data.GAME_VERSION_NUM +"";
        data.optionsObsfucator.enabled = false;
        EditorSettings.spritePackerMode = SpritePackerMode.SpriteAtlasV2;

        PlayerSettings.SetScriptingDefineSymbolsForGroup(BuildTargetGroup.iOS, symIOS + ";TEST_GAME");
        PlayerSettings.companyName = data.COMPANY_NAME;
        // アイコン設定
//        SetIcon(false);
        EditorSceneManager.SaveOpenScenes();
        AssetDatabase.SaveAssets ();
        bool error = false;
        if (!error && !BuildiOS(true)) error = true;

        // PlayerSettings.SetScriptingDefineSymbolsForGroup(BuildTargetGroup.iOS, symIOS);
        PlayerSettings.companyName = cn;
        EditorSettings.spritePackerMode = SpritePackerMode.Disabled;

//        if (error) EditorApplication.Exit(1);
	}

    [MenuItem("Window/Batch Build/Release Build AAB (Android)")]
    public static void ReleaseBuildAndroid()
    {
        PlayerSettings.SetScriptingBackend(BuildTargetGroup.Android, ScriptingImplementation.IL2CPP);
        PlayerSettings.SetArchitecture(BuildTargetGroup.Android, 3);
        AndroidArchitecture aac = AndroidArchitecture.ARM64 | AndroidArchitecture.ARMv7;
        PlayerSettings.Android.targetArchitectures = aac;
        PlayerSettings.stripEngineCode = true;
        EditorUserBuildSettings.buildAppBundle = true;
        LoadConfig();
        data.optionsObsfucator.enabled = true;

        var isBuild = data.isBuild;

        data.isBuild = false;
        var error = MakeBuildAndroid(false, false);
        // if(!error) AppBundlePublisher.Build();
        data.isBuild = isBuild;
    }

	[MenuItem("Window/Batch Build/(OLD) Release Build AAB (Android)")]
	public static void ReleaseBuildAndroidOld()
    {
        PlayerSettings.SetScriptingBackend(BuildTargetGroup.Android, ScriptingImplementation.IL2CPP);
        PlayerSettings.SetArchitecture(BuildTargetGroup.Android, 3);
        AndroidArchitecture aac = AndroidArchitecture.ARM64 | AndroidArchitecture.ARMv7;
        PlayerSettings.Android.targetArchitectures = aac;
        PlayerSettings.stripEngineCode = true;
        EditorUserBuildSettings.buildAppBundle = true;
        LoadConfig();
        data.optionsObsfucator.enabled = true;
        MakeBuildAndroid(false, false);
    }

    [MenuItem("Window/Batch Build/Test Build APK NO DEBUG (Android)")]
	public static void ReleaseBuildAPKAndroid()
    {
        LoadConfig ();
        PlayerSettings.SetScriptingBackend(BuildTargetGroup.Android, ScriptingImplementation.IL2CPP);
        PlayerSettings.SetArchitecture(BuildTargetGroup.Android, 3);
        AndroidArchitecture aac = AndroidArchitecture.ARM64 | AndroidArchitecture.ARMv7;
        PlayerSettings.Android.targetArchitectures = aac;
        PlayerSettings.stripEngineCode = true;
        EditorUserBuildSettings.buildAppBundle = false;
        data.optionsObsfucator.enabled = true;
        MakeBuildAndroid(false, true);
    }
    
    [MenuItem("Window/Batch Build/Release Build APK MONO (Android)")]
	public static void ReleaseBuildAPKMonoAndroid()
    {
        LoadConfig ();
        PlayerSettings.SetScriptingBackend(BuildTargetGroup.Android, ScriptingImplementation.Mono2x);
        PlayerSettings.SetArchitecture(BuildTargetGroup.Android, 3);
        AndroidArchitecture aac = AndroidArchitecture.ARMv7;
        PlayerSettings.Android.targetArchitectures = aac;
        PlayerSettings.stripEngineCode = true;
        EditorUserBuildSettings.buildAppBundle = false;
        data.optionsObsfucator.enabled = true;
        MakeBuildAndroid(false, false);
    }

    public static bool MakeBuildAndroid(bool isTestGame, bool isTestId){
        LoadConfig ();
        string symAnd = PlayerSettings.GetScriptingDefineSymbolsForGroup(BuildTargetGroup.Android);
        Debug.Log("symAnd " + symAnd);
        while(!isTestGame && symAnd.IndexOf(";TEST_GAME") >= 0)
        {
            symAnd = symAnd.Replace(";TEST_GAME", "");
            Debug.Log("REMOVE TEST_GAME symbol symAnd " + symAnd);
        }
        string cn = PlayerSettings.companyName;
        var appId = data.APP_ID + (isTestId ? "_test_" + BaseUtils.GetGitBranch().Replace(".", "_").Replace("-", "_") : "");
        Debug.Log("app id " + appId);
        PlayerSettings.applicationIdentifier =  appId;
        PlayerSettings.productName = (isTestGame ? BaseUtils.GetGitBranch() : "") + data.GAME_NAME;
        PlayerSettings.bundleVersion = data.GAME_VERSION;
        PlayerSettings.Android.bundleVersionCode = data.GAME_VERSION_NUM;
        PlayerSettings.Android.minifyRelease = false;
        PlayerSettings.Android.minifyDebug = false;
        // PlayerSettings.Android.minifyWithR8 = false;

        PlayerSettings.SetScriptingDefineSymbolsForGroup(BuildTargetGroup.Android, symAnd + (isTestGame ? ";TEST_GAME" : ""));
        PlayerSettings.companyName = data.COMPANY_NAME;
        EditorSettings.spritePackerMode = SpritePackerMode.SpriteAtlasV2;

        // set keystoreName.
        PlayerSettings.Android.keystoreName = data.keystorePath;
		// パスワードの再設定.
        PlayerSettings.Android.keystorePass = data.keystorePass;
        // alias名
        PlayerSettings.Android.keyaliasName = data.keyaliasName;
		// aliasのパスワードの再設定.
        PlayerSettings.Android.keyaliasPass = data.keyaliasPass;

        // アイコン設定
        //        SetIcon(false);
        #if SpriteImporterData
        SpriteImporterData.FixImagesImportByReSize();
        #endif

        EditorSceneManager.SaveOpenScenes();
        AssetDatabase.SaveAssets ();
        bool error = false;

        if(!isTestGame)
        {
            #if ENABLE_VOXELBUSTERS_ESSENTIAL_KIT
            var settings = VoxelBusters.EssentialKit.Editor.EssentialKitSettingsEditorUtility.DefaultSettings;
            if(settings.BillingServicesSettings.IsEnabled || settings.AddressBookSettings.IsEnabled || settings.NativeUISettings.IsEnabled || settings.SharingServicesSettings.IsEnabled || settings.CloudServicesSettings.IsEnabled || settings.GameServicesSettings.IsEnabled || settings.BillingServicesSettings.IsEnabled || settings.NetworkServicesSettings.IsEnabled || settings.NotificationServicesSettings.IsEnabled || settings.MediaServicesSettings.IsEnabled || settings.DeepLinkServicesSettings.IsEnabled)
            {
                Debug.LogError("ERROR: essential kit dang bi enable. update lai essential kit settings");
                error = true;
            }
            if(!settings.UtilitySettings.UsesStoreRatingUtility)
            {
                Debug.LogError("ERROR: essential UtilitySettings kit dang bi disable. update lai essential kit settings");
                error = true;
            }
            #endif
        }

        if(!isTestGame)
        {
            if(data.adsDataAndroid == null
            ||data.adsDataAndroid.admob_banner_id == "" 
            || data.adsDataAndroid.admob_full_video_id == "" 
            || data.adsDataAndroid.admob_reward_video_id == "" 
            || data.adsDataAndroid.admob_reward_full_id == "" 
            || data.adsDataAndroid.admob_app_open_ad_id == "") 
            {
                Debug.LogError("ERROR: id admob dang bi thieu hoac loi. update lai adsdata");
                error = true;
            }
        }
        error = error || CheckSongDataError();

        if(isTestGame)
        {
            MoveBundleToStreammingAsset();
        }
        else
        {
            error = error || UpdatePlayAssetDeliveryAndCheckHasError();
            DeleteAllStreammingAssets();
        }

        DeleteBeeAndroidFolder();

        if (!error && data.isBuild && !BuildAndroid(true)) error = true;

        if(isTestGame) PlayerSettings.SetScriptingDefineSymbolsForGroup(BuildTargetGroup.Android, symAnd);
        if(data.isBuild)
        {
            EditorSettings.spritePackerMode = SpritePackerMode.Disabled;
            PlayerSettings.companyName = cn;
        }
        return error;
//        if (error) EditorApplication.Exit(1);
	}

    [MenuItem("Window/Batch Build/Test Build (Android)")]
    public static void TestBuildAndroid()
    {

        PlayerSettings.SetScriptingBackend(BuildTargetGroup.Android, ScriptingImplementation.IL2CPP);
        PlayerSettings.SetArchitecture(BuildTargetGroup.Android, 3);
        AndroidArchitecture aac = AndroidArchitecture.ARM64 | AndroidArchitecture.ARMv7;
        PlayerSettings.Android.targetArchitectures = aac;
        PlayerSettings.stripEngineCode = true;
        EditorUserBuildSettings.buildAppBundle = false;
        LoadConfig();
        data.optionsObsfucator.enabled = false;
        MakeBuildAndroid(true, true);
    }
    
    [MenuItem("Window/Batch Build/Test Build with obfuscation (Android)")]
    public static void TestBuildAndroidWithObfuscation()
    {
        PlayerSettings.SetScriptingBackend(BuildTargetGroup.Android, ScriptingImplementation.Mono2x);
        PlayerSettings.SetArchitecture(BuildTargetGroup.Android, 5);
        EditorUserBuildSettings.buildAppBundle = false;
        LoadConfig();
        data.optionsObsfucator.enabled = true;
        MakeBuildAndroid(true, true);
    }

	// iOSビルド.
	private static bool BuildiOS(bool release){
        LoadConfig ();
		Debug.Log("Start Build( iOS )");

        BuildOptions opt = BuildOptions.None;
		// 開発用ビルドの場合のオプション設定.
        /*
		if (!release){
			opt |= BuildOptions.Development|BuildOptions.ConnectWithProfiler;
			EditorUserBuildSettings.symlinkLibraries = true;
			EditorUserBuildSettings.development = true;
		}
        */

		// ビルド.
		// シーン、出力ファイル（フォルダ）、ターゲット、オプションを指定.
		string errorMsg = "";
        bool error = false;
        if (data.adsDataIos != null)
        {
            if (data.adsDataIos.admob_banner_id == ""
            || data.adsDataIos.admob_full_video_id == ""
            || data.adsDataIos.admob_reward_video_id == ""
            || data.adsDataIos.admob_reward_full_id == ""
            || data.adsDataIos.admob_app_open_ad_id == "")
            {
                Debug.LogError("ERROR: id admob dang bi thieu hoac loi. update lai adsdata");
                error = true;
            }
        }
        if (!error && PlayerSettings.applicationIdentifier.Equals(data.APP_ID)) {
            errorMsg = BuildPipeline.BuildPlayer(scene, "../xcode_projects/" + data.APP_ID, BuildTarget.iOS, opt).summary.result.ToString();
		}
		// errorMsgがない場合は成功.
		if ( string.IsNullOrEmpty(errorMsg) ){
			Debug.Log("Build( iOS ) Success.");
			return true;
		}
		Debug.Log("Build( iOS ) ERROR!");
		Debug.LogError(errorMsg);
		return false;
	}

	// Androidビルド.
	private static bool BuildAndroid(bool release){
        LoadConfig ();
		Debug.Log("Start Build( Android )");
		 
		BuildOptions opt = BuildOptions.None;
		// 開発用ビルドの場合のオプション設定.
        /*
		if ( release==false ){
			opt |= BuildOptions.Development|BuildOptions.ConnectWithProfiler|BuildOptions.AllowDebugging;
		}
        */

		// ビルド.
		// シーン、出力ファイル（フォルダ）、ターゲット、オプションを指定.
		string errorMsg = "";

        Debug.Log(PlayerSettings.applicationIdentifier + " == " + data.APP_ID);
        var pathFile = "../Build/" + GameConfig.GAME_VERSION_STR + "." + BaseUtils.GetGitBranch().Replace('/','.') + "." + PlayerSettings.applicationIdentifier + "v" + PlayerSettings.bundleVersion + (EditorUserBuildSettings.buildAppBundle ? ".aab" : ".apk");
        Debug.Log("Build file : " + pathFile);
        if (PlayerSettings.applicationIdentifier.IndexOf(data.APP_ID) == 0)
        {
            errorMsg = BuildPipeline.BuildPlayer(scene, pathFile, BuildTarget.Android, opt).summary.result.ToString();
        }
		// errorMsgがない場合は成功
		if (string.IsNullOrEmpty(errorMsg)){
			Debug.Log("Build( Android ) Success.");
			return true;
		}
		Debug.Log("Build( Android ) ERROR!");
		Debug.LogError(errorMsg);
		return false;
	}

    /// <summary>
    /// アイコン設定
    /// </summary>
    /// <param name="isTest">テスト版かどうか</param>
    private static void SetIcon(bool isTest)
    {
        LoadConfig ();
        // 現在設定されているアイコンのテクスチャー
        Texture2D[] currentIcons;
        // 新しく設定するアイコンのテクスチャー
        Texture2D[] newIcons;
        // アイコンのpath
        string path = "Textures/Common/uchuneko";

        if (isTest)
            path = "Textures/Common/uchuneko-t";

        // 現在のアイコンを取得。
        currentIcons = PlayerSettings.GetIconsForTargetGroup(BuildTargetGroup.Unknown);

        // 新しいアイコンの配列を初期化。
        newIcons = new Texture2D[currentIcons.Length];

        // AssetsからIconデータ取得
        for(int i = 0; i < newIcons.Length; i++) {
            newIcons[i] = Resources.Load<Texture2D>(path);
        }

        // アイコン画像をPlayerSettingsに反映。
        PlayerSettings.SetIconsForTargetGroup(BuildTargetGroup.Unknown, newIcons);
    }
}