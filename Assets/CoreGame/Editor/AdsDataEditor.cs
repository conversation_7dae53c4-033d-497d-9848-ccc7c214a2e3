using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using GoogleMobileAds.Editor;

[CustomEditor(typeof(AdsData))]
public class AdsDataEditor : Editor
{
    public override void OnInspectorGUI()
    {
        if (GUILayout.Button("Import Data"))
        {
            var adsData = (AdsData)target;
            adsData.ImportData();
            
            EditorUtility.SetDirty(adsData);
            AssetDatabase.SaveAssets();
        }

        if(GUILayout.But<PERSON>("Update Admob ID"))
        {
            var adsData = (AdsData)target;
            GoogleMobileAdsSettings admobSetting = GoogleMobileAdsSettings.LoadInstance();
            if (adsData.admob_id != "")
            {
                #if UNITY_ANDROID
                admobSetting.GoogleMobileAdsAndroidAppId = adsData.admob_id;
                #else
                admobSetting.GoogleMobileAdsIOSAppId = adsData.admob_id;
                #endif                

                EditorUtility.SetDirty(admobSetting);
                AssetDatabase.SaveAssets();
            }
        }

        base.OnInspectorGUI();
    }
}
