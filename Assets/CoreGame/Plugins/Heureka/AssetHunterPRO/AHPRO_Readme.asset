%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7b27603d2be0e81488eb446378c435df, type: 3}
  m_Name: AHPRO_Readme
  m_EditorClassIdentifier: 
  Icon: {fileID: 2800000, guid: 94411db6691648e4590eb22360ba6934, type: 3}
  PackageShowPrio: 100
  AssetName: Asset Hunter PRO
  Subheader: Project Cleaning Tool
  AssetIdentifier: Heureka.AssetHunterPRO
  Description: 'Asset Hunter is THE project cleaning tool of the asset store.


    It helps you locate all the unused asset and delete them. It will reduce time
    spend waiting for the editor to load, it also simply boosts productivity in general
    by having to search through less stuff.


    Quick start:

    1: Open Asset Hunter PRO (Ctrl+H)

    2: Create a build

    3: Load newly created log into Asset Hunter PRO

    4: Inspect and delete unused assets

    5: Use settings to ignore certain types, folders etc.


    On the roadmap:

    Information on added assemblies [DONE]

    Improved unused script detection

    Buildinfo comparison

    Asset Dependency graph

'
  Links:
  - ActiveLink: 1
    Name: Asset Store
    Link: https://assetstore.unity.com/packages/tools/utilities/asset-hunter-pro-135296?aid=1011l4Izm
  - ActiveLink: 1
    Name: Documentation
    Link: http://heureka.dk/wp-content/uploads/2018/12/Documentation-Asset-Hunter-PRO.pdf
  - ActiveLink: 1
    Name: Forum Thread
    Link: http://forum.unity3d.com/threads/released-asset-hunter-project-cleaning.274173/
  - ActiveLink: 1
    Name: Youtube Promo
    Link: https://youtu.be/dxqrJhBKdbY
  - ActiveLink: 1
    Name: Youtube Introduction
    Link: https://youtu.be/m4_DcrhYhxI
  VersionData:
  - VersionNum:
      Major: 1
      Minor: 0
      Patch: 0
    VersionChanges:
    - Full re-write of Asset Hunter 2. New features, improved workflow, new UI and
      HUGE performance gains
    FoldOut: 0
  - VersionNum:
      Major: 1
      Minor: 1
      Patch: 0
    VersionChanges:
    - '[Feature] New window that showcases buildreport information (2018 or newer)'
    - '[Feature] Now able to remove text from buttons in settings'
    - '[Feature] Added a way to set default save location for userprefs in settings'
    - '[Feature] Added a way to set default save location for build info in settings'
    - '[Bug] Fixed issue with file endings not being excluding properly'
    FoldOut: 0
  - VersionNum:
      Major: 1
      Minor: 1
      Patch: 1
    VersionChanges:
    - '[Feature] Folders show accumulated disc size used'
    - '[UI Enhancement] Now the refresh button changes color when project is out of
      sync with buildinfo treeview'
    - '[Documentation] Added documentation for 1.1.0'
    FoldOut: 0
  - VersionNum:
      Major: 1
      Minor: 1
      Patch: 2
    VersionChanges:
    - '[Optimization] Now caches size of each folder, instead of calculating each
      frame'
    - '[Bug] Fixed an issue with loosing button icons after play/stop'
    FoldOut: 0
  - VersionNum:
      Major: 1
      Minor: 1
      Patch: 3
    VersionChanges:
    - '[Bug] Fixed issue with scripting compile symbols if AH2 was manually deleted'
    FoldOut: 0
  - VersionNum:
      Major: 1
      Minor: 1
      Patch: 4
    VersionChanges:
    - '[Bug] Added memory usage safeguard to fix issues with Unity running out of
      memory when analyzing very large projects'
    FoldOut: 0
  - VersionNum:
      Major: 1
      Minor: 1
      Patch: 5
    VersionChanges:
    - '[Bug] Fixed issue with assetbundles in streamingasset folder not adding all
      dependencies'
    FoldOut: 0
  - VersionNum:
      Major: 1
      Minor: 1
      Patch: 6
    VersionChanges:
    - '[Change] Changed the way we looks for assetbundle assets and dependencies.
      No more false errors from unity'
    - '[Change] Fixed compile warnings in 2018.3.'
    - '[Bug] Fixed issue with nullreference in rare cases when autogenerating buildreport'
    FoldOut: 0
  - VersionNum:
      Major: 1
      Minor: 1
      Patch: 7
    VersionChanges:
    - '[Feature] Now the Info button also opens the documentation file'
    FoldOut: 0
  - VersionNum:
      Major: 1
      Minor: 1
      Patch: 8
    VersionChanges:
    - '[Bug] Fixed issue with stackoverflow exception when two assetbundles referenced
      each other'
    FoldOut: 0
  - VersionNum:
      Major: 1
      Minor: 1
      Patch: 9
    VersionChanges:
    - '[Bug] Added a null check before testing file size'
    FoldOut: 0
  - VersionNum:
      Major: 1
      Minor: 2
      Patch: 0
    VersionChanges:
    - '[Feature] Added a way to save a given list of assets into a json file for external
      use'
    FoldOut: 0
  - VersionNum:
      Major: 1
      Minor: 2
      Patch: 1
    VersionChanges:
    - '[Bug] Fixed an issue with resource prefabs not having their dependencies added
      correctly'
    FoldOut: 0
  - VersionNum:
      Major: 1
      Minor: 2
      Patch: 2
    VersionChanges:
    - '[Bug] Fixed bug with assets referenced from nested prefabs inside resources
      folder'
    - '[Bug] Fixed issue with not being able to see used assets if the project was
      fully cleaned'
    FoldOut: 0
  - VersionNum:
      Major: 1
      Minor: 2
      Patch: 3
    VersionChanges:
    - '[Warning] Used precompile directive to remove warning for obsolete method in
      2019'
    FoldOut: 0
  - VersionNum:
      Major: 1
      Minor: 2
      Patch: 4
    VersionChanges:
    - '[Bug] Fixed an issue with size of folders not being calculated correctly'
    FoldOut: 0
  - VersionNum:
      Major: 1
      Minor: 2
      Patch: 5
    VersionChanges:
    - '[Bug] Cleaned up duplicate delegate subscription issue when opening and closing
      windows multiple times'
    FoldOut: 0
  - VersionNum:
      Major: 1
      Minor: 2
      Patch: 6
    VersionChanges:
    - '[Feature] Now Asset Hunter ignores .git folders inside the asset folder when
      deleting empty folders'
    FoldOut: 0
  - VersionNum:
      Major: 1
      Minor: 2
      Patch: 7
    VersionChanges:
    - Removed GC collect, since its performance implications outweighed the rare outofmemory
      issues
    FoldOut: 0
  - VersionNum:
      Major: 1
      Minor: 2
      Patch: 8
    VersionChanges:
    - Minor refactor
    FoldOut: 0
  - VersionNum:
      Major: 1
      Minor: 2
      Patch: 9
    VersionChanges:
    - '[Warning] Fixed facebook warning in 2019.3'
    FoldOut: 0
  - VersionNum:
      Major: 1
      Minor: 3
      Patch: 0
    VersionChanges:
    - '[Feature] Added support for Adressables'
    - '[Bug] Fixed issue that icon in colored bar didn''t scale'
    - '[Bug] Fixed bug with alignment of preview'
    FoldOut: 0
  - VersionNum:
      Major: 1
      Minor: 3
      Patch: 1
    VersionChanges:
    - Update to match other Heureka package global code
    FoldOut: 0
  - VersionNum:
      Major: 1
      Minor: 3
      Patch: 2
    VersionChanges:
    - Fix for Assembly Definitions in versions 2017.3 through 2018.4
    FoldOut: 0
  - VersionNum:
      Major: 1
      Minor: 3
      Patch: 3
    VersionChanges:
    - Fixed issue with import caused by lackluster asset update options
    FoldOut: 1
