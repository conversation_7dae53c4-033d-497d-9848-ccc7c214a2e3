using System.Runtime.InteropServices;
using UnityEngine;

public class AudioSessionSetter : Singleton<AudioSessionSetter>
{

    // -------------------------------------------------------------------------
    // Mono<PERSON><PERSON><PERSON><PERSON> Calls
    // -------------------------------------------------------------------------

    private void Awake()
    {
        SetAudioSession();
    }



    // -------------------------------------------------------------------------
    // Native Code Calls
    // -------------------------------------------------------------------------

#if UNITY_IOS && !UNITY_EDITOR
    [DllImport("__Internal")]
    private static extern void _SetAudioSession();

    // -------------------------------------------------------------------------
    public static void SetAudioSession()
    {
        _SetAudioSession();
    }
#else
    // -------------------------------------------------------------------------
    public static void SetAudioSession()
    {
        //not implemented --> fallback
    }
#endif
}
