﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System;

[CreateAssetMenu(fileName = "ScreenShotConfig", menuName = "Data/ScreenShotConfig", order = 1)]
public class ScreenShotConfig : ScriptableObject {
    public string MAIN_SCENE = "Loading";
    public string[] headerTextList = new string[]{
        "Play piano game",
        "Your favorite songs",
        "Easy to play",
        "Tap and hold to play\n with song Melody",
        "Brand-new level\n of sound quality"
    };
    public string[] secondTextList= new string[]{
        "Easy for everyone",
        "500+ songs",
        "Hard to Master",
        "",
        ""
    };
}
