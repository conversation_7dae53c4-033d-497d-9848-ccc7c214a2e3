﻿#if UNITY_EDITOR
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using UnityEngine.SceneManagement;
using System;
using UnityEditor;

public class ScreenShotStore : MonoBehaviour {
    /// <summary>
    /// Use Create/Data/SceenShotConfig to create new data in folder Resources/
    /// </summary>
    ScreenShotConfig data;
    public GameObject[] listScreenShot;

	// Use this for initialization
	void Start () {
        DontDestroyOnLoad (gameObject);
        data = Resources.Load<ScreenShotConfig>("ScreenShotConfig");
        for(int i = 0; i < listScreenShot.Length; i++){
            listScreenShot [i].transform.Find ("MainText").GetComponent<Text> ().text = data.headerTextList [i];
            listScreenShot [i].transform.Find ("SecondText").GetComponent<Text> ().text = data.secondTextList [i];
            try{
                listScreenShot [i].transform.Find ("MainImage").GetComponent<Image> ().sprite = Resources.Load<Sprite> ("screenshot_"+ (i + 1));
            } catch(Exception e){
                Debug.LogError (e.ToString ());
            }
        }
	}
	
	// Update is called once per frame
	void Update () {
        if (SceneManager.GetActiveScene ().name.Contains ("ScreenShot")) {
            if (Input.GetKeyDown (KeyCode.S)) {
                PlayerPrefs.DeleteAll ();
                SceneManager.LoadScene (data.MAIN_SCENE);
            }
            if (Input.GetKeyDown (KeyCode.E)) {
                StartCoroutine (CaptureResult ());
            }
        } else {
            if (Input.GetKeyDown (KeyCode.S)) {
                RandomCapture ();
            }
        }
        if (Input.GetKeyDown (KeyCode.Alpha1)) {
            ScreenCapture ("1" ,null, "Assets/Resources/");
        }

        if (Input.GetKeyDown (KeyCode.Alpha2)) {
            ScreenCapture ("2",null, "Assets/Resources/");
        }

        if (Input.GetKeyDown (KeyCode.Alpha3)) {
            ScreenCapture ("3",null, "Assets/Resources/");
        }

        if (Input.GetKeyDown (KeyCode.Alpha4)) {
            ScreenCapture ("4",null, "Assets/Resources/");
        }

        if (Input.GetKeyDown (KeyCode.Alpha5)) {
            ScreenCapture ("5",null, "Assets/Resources/");
        }
	}

    IEnumerator CaptureResult(){
        yield return new WaitForSeconds (0.2f);
        for (int i = 0; i < listScreenShot.Length; i++) {
            yield return new WaitForSeconds (0.2f);
            SetActive (i);
            ScreenCapture ("Result" + ( Screen.width + "x" + Screen.height ) + "_" + i,null,"Assets/Resources/");
        }
    }

    void SetActive(int id){
        for (int i = 0; i < listScreenShot.Length; i++) {
            if (i == id) {
                listScreenShot [i].SetActive (true);
            } else {
                listScreenShot [i].SetActive (false);
            }
        }
    }

    
    /// <summary>
    /// 全体もしくはRectでサイズ指定したスクリーンショットのPNGデータを生成
    /// </summary>
    /// <returns>The with capture.</returns>
    /// <param name="capchaCnt">キャプチャ回数</param>
    /// <param name="rect">Rect.</param>
    public static void ScreenCapture(string count, Rect? rect = null,string folder = "")
    {
        Debug.Log ("Captrue screenshots: " + count);
        string fileName =folder + "screenshot_" + count + ".png";
        if (rect == null) {
			UnityEngine.ScreenCapture.CaptureScreenshot(fileName);
        } else {
        }
    }

    public static void RandomCapture(){
        Debug.Log ("Random capture ");
        ScreenCapture (UnityEngine.Random.Range (0, 10000) + "");
    }
}
#endif
