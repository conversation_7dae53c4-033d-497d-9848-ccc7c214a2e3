= Asset Usage Detector =

Online documentation available at: https://github.com/yasirkula/UnityAssetUsageDetector
E-mail: <EMAIL>

1. ABOUT
This tool helps you find usages of the selected asset(s) and/or Object(s) in your Unity project, i.e. lists the objects that refer to them.

2. HOW TO
Simply open Window-Asset Usage Detector, select some assets, tweak the settings and GO!

3. KNOWN LIMITATIONS
- static variables are not searched
- GUIText materials are not searched
- Textures in Lens Flare's can not be searched