# CoreGame : coding

This project for all game core
namespace : com.sunmonkey.core_game

## Scene mac dinh
	Chung ta can clone 1 scene giong voi scene mac dinh moi khi tao scene moi, trong do co setup cua scene controller va popup controller, dong thoi canvas duoc scale theo moi man hinh

## Scene controller 
	Can ke thua class SceneControllerBase.cs
	Khi chuyen scene thi goi ham SceneChange("<ten_scene>");

## TransactionDataModel.cs (Singleton)
	- Mỗi game cần khai báo class này, nó sẽ chứa các transaction data giữa các screen
	- Screen sau khi loading sẽ gọi class này ra để lấy thông tin cần thiết tướng ứng transaction object truyền từ screen trước đó

## Popup Controller
	Luon them [Beebyte.Obfuscator.Skip] vao truoc ten class de k bi doi ten khi obfucate code -> loi hien popup
	Ke thua class BasePopupWindow.cs
	Duoc quan ly boi class PopupWindowManager.cs
	Ten class giong voi ten Prefab
	Keo prefab vao Component PopupWindowManager.cs
	Cach show popup : popupWindowManager.Get<CommonPopupWindow>().Show();

## Singleton.cs
	Su dung singleton bang cach ke thua : Singleton<T>

## CommonUITools.cs
	Su dung khi khoi tao 1 object moi
	Su dung khi load 1 anh tu server

## Toast
	Them ToastHolder object chua Toast nam trong canvas vao moi scene can dung
	Copy Toast prefab vao thu muc Resources cua game moi
	Goi ham ToastManager.showToast() de hien toast

==================================================================================

# Editor tools :
## TimeScaler.cs (Tools/TimeScaler)
	Dung de dieu chinh thoi gian nhanh cham khi test game.

## PlayerPrefsEditor.cs (Tools/PlayerPrefs/DeleteAll)
	Dung de xoa het data luu trong bo nho dem

## SceneViewWindow.cs (Tools/Scene View)
	Dung de hien tat ca nhung scene se xuat hien trong build cua game

## BuildBatch.cs
	Su dung Create/Data/BuildConfig de tao data build trong folder Resources/
	Window/Build Batch/BuildAndroid(Ios) de build game

==================================================================================

# Tools :
## IngameDebugConsole
	Dung de hien mot hop thoai Console khi chay game. Co the check log truc tiep o trong game.
	=> How : Them prefab IngameDebugConsole.prefab vao scene dau tien

## SceenShot Capture: ScreenShotStore.cs
	Use Create/Data/SceenShotConfig to create new data in folder Resources/
	Open scene ScreenShot or ScreenShotIpad to start capture
	1	nhan phim S(=Start) de bat dau		
	2	khi vao man hinh game thi an tu 1->5 (tuong ung voi 5 screenshots)		
	3	tat game di, chuyen cua so de Unity load lai anh		
	4	Bat game len va check xem anh da load len tren scene SceenShot chua		
	5	an E(=export) de export anh ra		
	6	Chay lenh node move_screenshot.text name=“folder_name”		
	7	check folder ../IconPiano/folder_name de xem anh		

=========================================================================================
# Toi uu game
## Toi uu anh trong game de giam dung luong build:
	Doi voi background: khong su dung tag, su dung dinh dang : RGB Crunch DTX1 ( Kich thuoc anh chia het cho 4 ) cho android va RGB CRunch ETC cho IOS
	Doi voi anh khac co alpha : su dung dinh dang RGBA Compressed ETC2 8bits cho Android va RGB Compressed PVRTC 4 bits cho IOS