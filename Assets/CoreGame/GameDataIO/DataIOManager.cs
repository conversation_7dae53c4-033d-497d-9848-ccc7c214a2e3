﻿using System;
using System.IO;
using System.Runtime.Serialization.Formatters.Binary;
using UnityEngine;

public class DataIOManager
{
    private static readonly DataIOManager ins = new DataIOManager();

    private DataIOManager() { }

    public static DataIOManager GetInstance() { return ins; }

    //public void Save<T>(T target, string key = null) where T : class
    //{
    //    if (!typeof(T).IsSerializable)
    //    {
    //        throw new Exception();
    //    }

    //    string jsonString = JsonUtility.ToJson(target, true);
    //    key = string.IsNullOrEmpty(key) ? typeof(T).Name : key;

    //    PlayerPrefs.SetString(key, jsonString);

    //    PlayerPrefs.Save();
    //}

    public void Save<T>(T target, string key = null) where T : class
    {
		var fileName = key == null ? "gamedata.dat" : key + ".dat";
		using (var fs = File.Open(Application.persistentDataPath + "/" + fileName,
                                        FileMode.OpenOrCreate,
                                        FileAccess.ReadWrite,
                                        FileShare.None))
        {
            var bf = new BinaryFormatter();
            bf.Serialize(fs, target);
        }
    }

    //public T Load<T>(string key = null) where T : class
    //{
    //    if (!typeof(T).IsSerializable)
    //    {
    //        throw new Exception();
    //    }

    //    key = string.IsNullOrEmpty(key) ? typeof(T).Name : key;

    //    string jsonString = PlayerPrefs.GetString(key);

    //    return string.IsNullOrEmpty(jsonString) ? null : JsonUtility.FromJson<T>(jsonString);
    //}

    public T Load<T>(string key = null) where T : class
    {
		var fileName = key == null ? "gamedata.dat" : key + ".dat";
		if (File.Exists(Application.persistentDataPath + "/" + fileName))
        {
            try
            {
				using (var fs = File.Open(Application.persistentDataPath + "/" + fileName,
                                            FileMode.OpenOrCreate,
                                            FileAccess.ReadWrite,
                                            FileShare.None))
                {
                    var bf = new BinaryFormatter();
                    var target = (T)bf.Deserialize(fs);
                    fs.Close();

                    return target;
                }
            }
            catch
            {
                return null;
            }
        }
        else
        {
            return null;
        }
    }

    public void DeleteAll()
    {
        foreach (var file in Directory.GetFiles(Application.persistentDataPath))
        {
            Debug.Log("File :: " + file);
            if (file.Contains(".dat")) {
                FileInfo file_info = new FileInfo(file);
                file_info.Delete();
            }
        }
    }
}
