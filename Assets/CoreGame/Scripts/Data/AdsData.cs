using System.Collections;
using System.Collections.Generic;
using UnityEngine;

[CreateAssetMenu(fileName = "AdsData", menuName = "Data/AdsData", order = 1)]
public class AdsData : ScriptableObject
{
    static AdsData ins;
    public string UNITY_ADS_ID = "4056073";
    [TextArea] public string inputAdmob;
    public MappingKey mapping_admob_banner_id;
    public MappingKey mapping_admob_full_video_id;
    public MappingKey mapping_admob_reward_video_id;
    public MappingKey mapping_admob_reward_full_id;
    public MappingKey mapping_admob_app_open_ad_id;
    public MappingKey mapping_admob_banner_no_refresh_id;
    public string admob_id = "";
    public string admob_banner_id = "";
    public string admob_full_video_id = "";
    public string admob_reward_video_id = "";
    public string admob_reward_full_id = "";
    public string admob_app_open_ad_id = "";
    public string admob_banner_no_refresh_id = "";

    public string unity_full_video_id = "video";
    public string unity_full_reward_id = "rewardedVideo";
    public string unity_banner_id = "Banner_Android";

    public void ImportData()
    {
        if(inputAdmob == "") Debug.LogError("Please enter input admob");
        var texts = inputAdmob.Split('\n');
        if(texts.Length % 2 != 0) Debug.LogError("So lượng dòng text cần phải là số chẵn");

        for (int i = 0; i < texts.Length; i++)
        {
            if(i % 2 == 0)
            {
                Debug.Log(texts[i]);
                if(texts[i].ToLower().Contains(mapping_admob_banner_id.key) && (mapping_admob_banner_id.notKey == ""  || !texts[i].ToLower().Contains(mapping_admob_banner_id.notKey)))
                {
                    admob_banner_id = texts[i + 1].Replace("\r", "");
                }
                if(texts[i].ToLower().Contains(mapping_admob_full_video_id.key) && (mapping_admob_full_video_id.notKey == ""  || !texts[i].ToLower().Contains(mapping_admob_full_video_id.notKey)))
                {
                    admob_full_video_id = texts[i + 1].Replace("\r", "");
                }
                if(texts[i].ToLower().Contains(mapping_admob_reward_video_id.key) && (mapping_admob_reward_video_id.notKey == ""  || !texts[i].ToLower().Contains(mapping_admob_reward_video_id.notKey)))
                {
                    admob_reward_video_id = texts[i + 1].Replace("\r", "");
                }
                if(texts[i].ToLower().Contains(mapping_admob_reward_full_id.key) && (mapping_admob_reward_full_id.notKey == ""  || !texts[i].ToLower().Contains(mapping_admob_reward_full_id.notKey)))
                {
                    admob_reward_full_id = texts[i + 1].Replace("\r", "");
                }
                if(texts[i].ToLower().Contains(mapping_admob_app_open_ad_id.key) && (mapping_admob_app_open_ad_id.notKey == ""  || !texts[i].ToLower().Contains(mapping_admob_app_open_ad_id.notKey)))
                {
                    admob_app_open_ad_id = texts[i + 1].Replace("\r", "");
                }
                if (texts[i].ToLower().Contains(mapping_admob_banner_no_refresh_id.key) && (mapping_admob_banner_no_refresh_id.notKey == "" || !texts[i].ToLower().Contains(mapping_admob_banner_no_refresh_id.notKey)))
                {
                    admob_banner_no_refresh_id = texts[i + 1].Replace("\r", "");
                }
            }
        }
        admob_id = texts[1].Replace("\r", "");

        if(admob_id == "" || !admob_id.Contains("~"))
        {
            Debug.LogError("admob_id bi thieu - chay lai data di - admob id luon phai la dong thu 2 va id phai co dau ~ " + admob_id);
            admob_id = "";
            return;
        }
        if(admob_banner_id == "" || !admob_banner_id.Contains("/"))
        {
            Debug.LogError("admob_banner_id bi thieu hoac sai cu phap - chay lai data di " + admob_banner_id);
            admob_banner_id = "";
            return;
        }
        if(admob_full_video_id == "" || !admob_full_video_id.Contains("/"))
        {
            Debug.LogError("admob_full_video_id bi thieu hoac sai cu phap - chay lai data di " + admob_full_video_id);
            admob_full_video_id = "";
            return;
        }
        if(admob_reward_video_id == "" || !admob_reward_video_id.Contains("/"))
        {
            Debug.LogError("admob_reward_video_id bi thieu hoac sai cu phap - chay lai data di " + admob_reward_video_id);
            admob_reward_video_id = "";
            return;
        }
        if(admob_reward_full_id == "" || !admob_reward_full_id.Contains("/"))
        {
            Debug.LogError("admob_reward_full_id bi thieu hoac sai cu phap - chay lai data di " + admob_reward_full_id);
            admob_reward_full_id = "";
            return;
        }
        if(admob_app_open_ad_id == "" || !admob_app_open_ad_id.Contains("/"))
        {
            Debug.LogError("admob_app_open_ad_id bi thieu hoac sai cu phap - chay lai data di " + admob_app_open_ad_id);
            admob_app_open_ad_id = "";
            return;
        }

        

        Debug.Log("Success!!!! Import thanh cong!!!!!");
    }

    public static AdsData adsData(){
        if(ins != null)
        {
            if (GameConfig.ADMOB_BANNER_ID != "") ins.admob_banner_id = GameConfig.ADMOB_BANNER_ID;
            if (GameConfig.ADMOB_FULL_ID != "") ins.admob_full_video_id = GameConfig.ADMOB_FULL_ID;
            if (GameConfig.ADMOB_REWARD_ID != "") ins.admob_reward_video_id = GameConfig.ADMOB_REWARD_ID;
            if (GameConfig.ADMOB_REWARD_FULL_ID != "") ins.admob_reward_full_id = GameConfig.ADMOB_REWARD_FULL_ID;
            if (GameConfig.ADMOB_OPEN_ADS_ID != "") ins.admob_app_open_ad_id = GameConfig.ADMOB_OPEN_ADS_ID;
            if (GameConfig.ADMOB_BANNER_NO_REFRESH_ID != "") ins.admob_banner_no_refresh_id = GameConfig.ADMOB_BANNER_NO_REFRESH_ID;
            return ins;
        }
        var name = "AdsData";
        var test = "";
        var platform = "Android";
        #if TEST_GAME || UNITY_EDITOR
        test = "Test";
        #endif
        #if UNITY_IOS
        platform = "Ios";
        #endif
        ins = Resources.Load<AdsData>(test + platform + name);
        return ins;
    }
}
