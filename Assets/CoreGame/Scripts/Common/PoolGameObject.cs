﻿using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using System;

public class PoolGameObject
{
	public GameObject prefabOrigin;
	public List<GameObject> listAvai;
	public List<GameObject> listInUse;
    public Action<GameObject> onCreateNew;
    public PoolGameObject(){
		listAvai = new List<GameObject> ();
		listInUse = new List<GameObject> ();
	}

    public PoolGameObject(Action<GameObject> onCreateNew){
		listAvai = new List<GameObject> ();
		listInUse = new List<GameObject> ();
		this.onCreateNew = onCreateNew;
	}

	public void cacheNewAndUse(GameObject g){
		listInUse.Add(g);
	}

    public IEnumerator PreCachedCor(GameObject gameObject, Transform parent, int count)
    {
		if(listAvai.Count >= count) yield break;
        for (int i = 0; i < count; i++)
        {
            PreCached(gameObject, parent);
			yield return null;
        }
    }
    
	public void PreCached(GameObject gameObject, Transform parent)
    {
		var g = GameObject.Instantiate(gameObject, parent);
		g.SetActive(false);
		listAvai.Add(g);
		if(g.GetComponent<PoolItemMonoBehaviour>() != null) g.GetComponent<PoolItemMonoBehaviour>().pool = this;
		if(onCreateNew != null) onCreateNew(g);
		this.prefabOrigin = gameObject;
    }

	public GameObject getInCache(bool autoCreateNew = true){
		while(listAvai.Count > 0) {
			GameObject g = listAvai [0];
			listAvai.RemoveAt (0);
			if (g == null) {
				continue;
			}
			listInUse.Add (g);
			g.SetActive (true);
			return g;
		}

		if (!autoCreateNew) return null;

		var prefab = prefabOrigin ?? (listInUse.Count > 0 && listInUse[0] != null ? listInUse[0] : null);
		var parent = listInUse.Count > 0 && listInUse[0] != null ? listInUse[0].transform.parent : (prefabOrigin == null ? null : prefabOrigin.transform.parent);

		if (prefab != null && parent != null)
        {
            PreCached(prefab, parent);
            return getInCache();
        }

		return null;
	}

	public void SetTimeBackToCached(GameObject g, float timeBackToCache){
        if (timeBackToCache > 0) CoroutineFunction.Instance.StartCoroutine(BackToPoolEffect(g, timeBackToCache));
    }

	public void backToCache(GameObject g){
		listInUse.Remove (g);
		if (!listAvai.Contains(g)) listAvai.Add (g);
	}

	public void backToCache(GameObject g, bool isVisible){
		g.SetActive (isVisible);
		listInUse.Remove (g);
        if (!listAvai.Contains(g)) listAvai.Add(g);
	}

	public void deleteAllCurrent(){
		foreach (GameObject g in listInUse) {
			g.SetActive (false);
		}
		listAvai.AddRange (listInUse);
		listInUse.Clear ();
	}

	public void destroyAll() {
		foreach (GameObject g in listInUse) {
			GameObject.DestroyImmediate (g);
		}
		foreach (GameObject g in listAvai) {
			GameObject.DestroyImmediate (g);
		}
		listInUse.Clear ();
		listAvai.Clear ();
	}

    protected IEnumerator BackToPoolEffect(GameObject g, float time = 1)
    {
        WaitForSeconds waitTouchEffect = new WaitForSeconds(time);
        yield return waitTouchEffect;
        backToCache(g);
    }
}

public class PoolItemMonoBehaviour : MonoBehaviour
{
	public PoolGameObject pool;
}