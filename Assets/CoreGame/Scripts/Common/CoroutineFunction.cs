﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System;

public class CoroutineFunction : SingletonMonoBehaviour<CoroutineFunction> {

	public void PlayAfterTime(float timeDelay, Action func)
	{
		StartCoroutine(playFunctionAfterTime(timeDelay, func));
	}

	private IEnumerator playFunctionAfterTime(float time, Action func) {
		yield return new WaitForSeconds (time);
		if (func != null) {
			func ();
		}
	}
}
