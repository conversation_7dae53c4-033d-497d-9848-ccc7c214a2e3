﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using System;

public class NumberRunning : MonoBehaviour {
	public Text text;
	public double numFrom;
	public double numTo;
	public string prefix;
	public string suffix;
	public float timeRun;
	public bool isDotFormat;
	public bool isSlowDownAtEnd = false;
	public float slowDownRange = 5;
	public float slowDownOffset = 50f;
	public AudioClip sound;
	public float soundTimeLength = 0.2f;
	Action _callback;
	private double _offset;
	private bool _isRunning;
	private float _timePlaySound;
	private int numberPlaySound = 0;

	void Start () {
		if (text == null) {
			text = GetComponent<Text> ();
		}
	}
	
	// Update is called once per frame
	void Update () {
		if (_isRunning) {
			var offset = _offset * Time.deltaTime;
			if(isSlowDownAtEnd && Math.Abs(numTo - numFrom) < slowDownRange) {
				_offset = slowDownOffset;
				isSlowDownAtEnd = false;
			}
			if (this._offset > 0) {
				if (numFrom < numTo - offset) {
					numFrom += offset;
				} else {
					numFrom = numTo;
					_isRunning = false;
					if (_callback != null) {
						_callback ();
					}
				}
			} else {
				if (numFrom > numTo - offset) {
					numFrom += offset;
				} else {
					numFrom = numTo;
					_isRunning = false;
					if (_callback != null) {
						_callback ();
					}
				}
			}
			text.text = prefix + getNumberStr (numFrom) + suffix;
			if(sound != null) {
				var currentNumber = (int)numFrom;
				if(currentNumber != numberPlaySound && _timePlaySound > soundTimeLength) 
				{
					numberPlaySound = currentNumber;
					_timePlaySound = 0;
					AudioManager2.Instance.PlaySFX(sound);
				}
				_timePlaySound += Time.deltaTime;
			}
		}
	}

	public void startRun(double numFrom, double numTo, float timeRun, Action cb = null) {
		this.numFrom = numFrom;
		this.numTo = numTo;
		this._offset = ((numTo - numFrom) / (timeRun));
		Debug.Log ("_offset: " + _offset + " numFrom: " + numFrom + " numTo: " + numTo + " timeRun: " + timeRun + " Time.deltaTime: " + Time.deltaTime);
		// if (this._offset == 0 && this.numFrom < this.numTo) {
		// 	this._offset = 1;
		// } else if (this._offset == 0 && this.numFrom > this.numTo) {
		// 	this._offset = -1;
		// }
		this._isRunning = true;
		this._callback = cb;
		if (_offset == 0 && cb != null)
			cb ();
	}

	public void setText(string txt) {
		text.text = txt;
	}

	public void setPrefix(string txt) {
		prefix = txt;
	}

	public void setSuffic(string txt) {
		suffix = txt;
	}

	string getNumberStr(double number) {
		if (isDotFormat) {
			return CommonFunction.getNumberDot (number);
		}
		return (int)number + "";
	}
}
