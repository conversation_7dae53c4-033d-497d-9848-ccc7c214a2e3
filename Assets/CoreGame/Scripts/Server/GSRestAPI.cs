﻿using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Networking;

public class GSRestAPI : MonoBehaviour
{
    public enum STAGE { LIVE, PREVIEW };
    public static GSRestAPI Instance;

    public STAGE stage;
    public string api_key = "A354040FBx1l";
    public string credential = "SS";
    public string credential_secret = "RSNcbK34BvcF9VVUpW4sfWMZXLtGUjZc";
    public string backupServerUrl = "https://banner-data.s3.us-east-2.amazonaws.com";
    public bool isUseBackupServer = false;

    void Awake(){
        if (Instance != null)
        {
            Destroy(gameObject);
            return;
        }
        DontDestroyOnLoad(gameObject);
        Instance = this;
    }

    public void GetRequest(string json, Action<string> successCallback, Action failCallback, string backupObject = ""){
        StartCoroutine(GetRequestCor(json, successCallback, failCallback, backupObject));
    }

    IEnumerator GetRequestCor(string json, Action<string> successCallback, Action failCallback, string backupObject = "")
    {
        var url = "https://" + api_key + "." + stage.ToString() + ".gamesparks.net/callback/" + api_key + "/" + credential + "/" + credential_secret;
        var uwr = new UnityWebRequest(url, "POST");
        if(isUseBackupServer && backupObject != ""){
            url = backupServerUrl + "/" + backupObject;
            uwr = new UnityWebRequest(url, "GET");
        }
        byte[] jsonToSend = new System.Text.UTF8Encoding().GetBytes(json);
        uwr.uploadHandler = (UploadHandler)new UploadHandlerRaw(jsonToSend);
        uwr.downloadHandler = (DownloadHandler)new DownloadHandlerBuffer();
        uwr.SetRequestHeader("Content-Type", "application/json;charset=utf-8");

        //Send the request then wait here until it returns
        yield return uwr.SendWebRequest();

        if (uwr.result == UnityWebRequest.Result.ConnectionError)
        {
            Debug.LogWarning("Error While Sending: " + uwr.error);
            if(failCallback != null) failCallback();
        }
        else
        {
            if(successCallback != null) successCallback(uwr.downloadHandler.text);
        }
    }
}
