﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class MovingLopUI : MonoBehaviour
{
    public float range = 3f;
    public float speed = 2f;
    public bool isHorizontal = true;

    float xPos;
    float offset;
    Vector3 newPos;
    RectTransform rectTransform;

    private void OnEnable()
    {
        offset = Random.Range(0f, 1f) / speed;
        speed *= Random.Range(0f, 1f) > 0.5 ? 1 : -1;
        rectTransform = GetComponent<RectTransform>();
    }

    private void Update()
    {
        float t = (Mathf.Cos((Time.time + offset) * speed) + 1) / 2;
        xPos = Mathf.Lerp(-range, range, t);
        if (isHorizontal)
        {
            newPos = new Vector2(xPos, rectTransform.anchoredPosition.y);
        }
        else
        {
            newPos = new Vector2(rectTransform.anchoredPosition.x, xPos);
        }

        rectTransform.anchoredPosition = newPos;
    }
}
