﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using DG.Tweening;

public class ShowAnimationTranform : MonoBehaviour
{
    public enum ShowAnimType { ZOOM_IN, ZOOM_OUT, SHAKE , MOVE_FROM_BOTTOM, MOVE_FROM_TOP, MOVE_FROM_LEFT, MOVE_FROM_RIGHT};
    public ShowAnimType showAnimType = ShowAnimType.ZOOM_OUT;
    public float timeAnim = 0.5f;
    public float maxZoom = 5f;
    public float minZoom = 0f;
    public float maxWidth = 3000f;
    public float maxHeight = 3000f;
    private Vector3 defaultScale;
    private Vector2 defaultPostion;
    private Transform t;

    /// <summary>
    /// Awake is called when the script instance is being loaded.
    /// </summary>
    void Awake()
    {
        defaultScale = transform.localScale;
        t = GetComponent<Transform>();
        if(t== null) return;
        defaultPostion = t.localPosition;
        OnDisable();
    }

    /// <summary>
    /// This function is called when the object becomes enabled and active.
    /// </summary>
    void OnEnable()
    {
        if(t == null) return;
        switch (showAnimType)
        {
            case ShowAnimType.ZOOM_IN:
            t.DOScale(defaultScale, timeAnim);
            break;
            case ShowAnimType.ZOOM_OUT:
            t.DOScale(defaultScale, timeAnim);
            break;
            case ShowAnimType.MOVE_FROM_BOTTOM:
            t.DOLocalMoveX(defaultPostion.y, timeAnim);
            break;
            case ShowAnimType.MOVE_FROM_TOP:
            t.DOLocalMoveY(defaultPostion.y, timeAnim);
            break;
            case ShowAnimType.MOVE_FROM_LEFT:
            t.DOLocalMoveX(defaultPostion.x, timeAnim);
            break;
            case ShowAnimType.MOVE_FROM_RIGHT:
            t.DOLocalMoveX(defaultPostion.x, timeAnim);
            break;
            case ShowAnimType.SHAKE:
            t.DOShakeRotation(timeAnim,10,10,45);
            break;
        }

    }

    /// <summary>
    /// This function is called when the behaviour becomes disabled or inactive.
    /// </summary>
    void OnDisable()
    {
        if(t == null) return;
        switch (showAnimType)
        {
            case ShowAnimType.ZOOM_IN:
            t.DOScale(maxZoom * defaultScale, 0);
            break;
            case ShowAnimType.ZOOM_OUT:
            t.DOScale(minZoom * defaultScale, 0);
            break;
            case ShowAnimType.MOVE_FROM_BOTTOM:
            t.DOLocalMoveY(defaultPostion.y - maxHeight, 0);
            break;
            case ShowAnimType.MOVE_FROM_TOP:
            t.DOLocalMoveY(defaultPostion.y + maxHeight, 0);
            break;
            case ShowAnimType.MOVE_FROM_LEFT:
            t.DOLocalMoveX(defaultPostion.x - maxWidth, 0);
            break;
            case ShowAnimType.MOVE_FROM_RIGHT:
            t.DOLocalMoveX(defaultPostion.x + maxWidth, 0);
            break;
            case ShowAnimType.SHAKE:
            break;
        }
    }
}
