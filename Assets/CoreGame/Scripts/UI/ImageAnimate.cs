﻿using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

[DisallowMultipleComponent]
public class ImageAnimate : MonoBehaviour {
    public Sprite[] spriteList;
    private Sprite[] currentSpriteList = new Sprite[] { };
    public List<ImageAnimateData> dataList;
    public float timeChange;
    public bool isLoop = true;
    public bool autoPlay = true;

    private Image image;
    private SpriteRenderer sprite;
    private float timeNow;
    private int index;
    private bool playing = false;
    void Start(){
        image = GetComponent<Image>();
        sprite = GetComponent<SpriteRenderer>();
        currentSpriteList = spriteList;
        if(autoPlay) Play();
    }

    public void Play(){
        index = 0;
        timeNow = 0;
        playing = true;
        currentSpriteList = spriteList;
        UpdateSprite(index);
    }

    public void Play(string name)
    {
        for(int i = 0; i < dataList.Count; i++)
        {
            if(dataList[i].name == name)
            {
                currentSpriteList = dataList[i].spriteList;
                index = 0;
                timeNow = 0;
                playing = true;
                UpdateSprite(index);
                return;
            }
        }
        Play();
    }

    public void Stop(){
        playing = false;
        index = currentSpriteList.Length - 1;
        UpdateSprite(index);
    }

    private void Update()
    {
        if(currentSpriteList.Length == 0 || (image == null && sprite == null) || timeChange <= 0 || !playing)
        {
            return;
        }
        if(!isLoop && index > currentSpriteList.Length) return;

        timeNow += Time.deltaTime;
        if(timeNow > timeChange)
        {
            timeNow -= timeChange;
            UpdateSprite(index++);
            if(index >= currentSpriteList.Length && !isLoop) playing = false;
        }
    }

    public void UpdateSprite(int index){
        if (currentSpriteList.Length > 0)
        {
            if (image != null) image.sprite = currentSpriteList[index % currentSpriteList.Length];
            if (sprite != null)
            {
                sprite.sprite = currentSpriteList[index % currentSpriteList.Length];
            }
        }
    }

    public void UpdateSpriteList(Sprite[] _spriteList)
    {
        this.spriteList = _spriteList;
        currentSpriteList = _spriteList;
        Play();
    }
}

[Serializable]
public class ImageAnimateData
{
    public string name;
    public Sprite[] spriteList;
}
