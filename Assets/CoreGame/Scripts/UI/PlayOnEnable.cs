﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class PlayOnEnable : MonoBehaviour {
    public string soundFx = "";
	// Use this for initialization
	void OnEnable () {
        var effect = GetComponent<ParticleSystem> ();
        if (effect != null) {
            effect.Play ();
        }
        if(soundFx != ""){
            AudioManager2.Instance.PlaySFX(soundFx);
        }
	}
}
