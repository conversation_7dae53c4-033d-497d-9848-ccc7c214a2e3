using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using System;
using System.Linq;

public class CustomSelectOptionUI : MonoBehaviour
{
    private int currentIdx = -1;
    private int maxIdx;
    public bool isCircleLoop;
    public GameObject[] optionObjs;
    [SerializeField]
    private Button btnNext;
    [SerializeField]
    private Button btnPrevious;
    [SerializeField]
    private Button btnSelection;

    public Action<int> onNextAction;
    public Action<int> onPreviousAction;

    void Start()
    {
        maxIdx = optionObjs.Length;

        btnNext.onClick.RemoveAllListeners();
        btnNext.onClick.AddListener(OnNext);

        btnPrevious.onClick.RemoveAllListeners();
        btnPrevious.onClick.AddListener(OnPrevious);

        if (btnSelection != null)
        {
            btnSelection.onClick.RemoveAllListeners();
            btnSelection.onClick.AddListener(OnNext);
        }

        SetCurrentSelection(0);
    }

    public int GetCurrentSelection()
    {
        return currentIdx;
    }

    public void SetCurrentSelection(int index)
    {
        if (index != currentIdx && index >= 0 && index < maxIdx)
        {
            currentIdx = index;
            UpdateOptionObj();
            UpdateBtnStatus();
        }
    }

    private void UpdateBtnStatus()
    {
        if (!isCircleLoop)
        {
            btnNext.gameObject.SetActive(currentIdx < maxIdx - 1);
            btnPrevious.gameObject.SetActive(currentIdx > 0);
        }
    }

    private void UpdateOptionObj()
    {
        for (var i = 0; i < maxIdx; i++)
        {
            optionObjs[i].SetActive(i == currentIdx);
        }
    }

    private void OnNext()
    {
        if (!isCircleLoop && currentIdx >= maxIdx - 1) return;
        currentIdx++;
        if (currentIdx == maxIdx) currentIdx = 0;
        UpdateBtnStatus();
        UpdateOptionObj();
        if (onNextAction != null) onNextAction(currentIdx);
    }

    private void OnPrevious()
    {
        if (!isCircleLoop && currentIdx <= 0) return;
        currentIdx--;
        if (currentIdx < 0) currentIdx = maxIdx - 1;
        UpdateBtnStatus();
        UpdateOptionObj();
        if (onPreviousAction != null) onPreviousAction(currentIdx);
    }
}
