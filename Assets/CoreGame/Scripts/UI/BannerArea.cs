﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System;
using UnityEngine.Events;
using System.Linq;

//Change init banner to this
//AdSize adaptiveSize = AdSize.GetCurrentOrientationAnchoredAdaptiveBannerAdSizeWithWidth(AdSize.FullWidth);
//this.bannerView = new BannerView(admob_banner_id, adaptiveSize, AdPosition.Bottom);
//Add this to banner loaded function
//Debug.Log("Banner size " + bannerView.GetWidthInPixels() + "x" + bannerView.GetHeightInPixels());
//				BannerArea.heightDevideWidth = bannerView.GetHeightInPixels() / bannerView.GetWidthInPixels();
public class BannerArea : MonoBehaviour {
	public static float heightDevideWidth = 0;
    public static BannerArea ins;

	private float currentHeightDevideWidth = -1;
	public RectTransform[] listReposition;
    public delegate void OnSizeChanged();
    public OnSizeChanged onSizeChanged;
    public UnityEvent onShowBanner;
    public UnityEvent onHideBanner;

    private void Awake()
    {
        ins = this;
        transform.SetAsLastSibling();
    }

    private void Update()
    {

        if (Input.GetKeyDown(KeyCode.B))
        {
            heightDevideWidth = 0.1f;
        }

        if(currentHeightDevideWidth != heightDevideWidth)
        {
            currentHeightDevideWidth = heightDevideWidth;
            UpdateSize(heightDevideWidth);
        }
    }

    // Use this for initialization
    void UpdateSize(float heightDivideWidth)
    {
        var canvas = GetComponentInParent<Canvas>();
        var rectTransform = GetComponent<RectTransform>();

        int height = (int)(heightDivideWidth * canvas.GetComponent<RectTransform>().sizeDelta.x + 10);
        rectTransform.sizeDelta = new Vector2(rectTransform.sizeDelta.x, height - rectTransform.anchoredPosition.y);

        foreach (var t in listReposition)
        {
            if (t != null) t.offsetMin = new Vector2(t.offsetMin.x, height);
        }

        if (onSizeChanged != null) onSizeChanged.Invoke();
        if (heightDivideWidth > 0)
        {
            onShowBanner.Invoke();
        }
        else
        {
            onHideBanner.Invoke();
        }

    }

    public Vector3 ScreenToWorldInOtherCamera(Vector3 screenPosition, Camera targetCamera)
    {
        Ray ray = targetCamera.ScreenPointToRay(screenPosition);
        Plane plane = new Plane(Vector3.forward, Vector3.zero); // Điều chỉnh mặt phẳng nếu cần
        if (plane.Raycast(ray, out float distance))
        {
            return ray.GetPoint(distance);
        }
        return Vector3.zero;
    }
    public void Show()
    {
        gameObject.SetActive(true);
        // AdsManager.ins.showBannerView();
    }

    public void Hide()
    {
        gameObject.SetActive(false);
        // AdsManager.ins.hideBannerView();
    }
}
