﻿using UnityEngine;
using UnityEngine.UI;

[Beebyte.Obfuscator.Skip]
[ExecuteInEditMode]
public class CanvasScaleMatchModifier : MonoBehaviour
{
    // we reference to resolution 640:1138, aspec ration is 9 : 16
    private readonly Vector2 ReferenceResolution = new Vector2(640f, 640f * 16f / 9f);
    private float lastScreenWidth = 0;
    CanvasScaler canvasScaler;

    void Awake()
    {
        canvasScaler = GetComponent<CanvasScaler>();
        UpdateScaleFactor();
    }

    void Update()
    {
        if (Application.isEditor)
        {
            UpdateScaleFactor();
        }
    }

    void UpdateScaleFactor()
    {
        // In Editor, Screen.width and Screen.height not correct
        Vector2 screenSize = GetScreenSize();
	
        if (lastScreenWidth == screenSize.x)
        {
            return;
        }

        lastScreenWidth = screenSize.x;
        canvasScaler.matchWidthOrHeight = 0f; // we set canvas scaler math to 0, that mean we will math canvas to Width of reference resolution

        // Check height of canvas in case we set canvas scale match width
        float height = ReferenceResolution.x * screenSize.y / screenSize.x;
        if (height < ReferenceResolution.y)
        {  // Canvas scale match width and height < ReferenceResolution Height => We need to math to height
            canvasScaler.matchWidthOrHeight = 1f;
        }
        Canvas.ForceUpdateCanvases(); // force canvas update conten
    }

    public static Vector2 GetScreenSize()
    {
        if (Application.isEditor)
        {
            return GetMainGameViewSize();
        }
        else
        {
            return new Vector2(Screen.width, Screen.height);
        }
    }

    public static Vector2 GetMainGameViewSize()
    {
        System.Type T = System.Type.GetType("UnityEditor.GameView,UnityEditor");
        System.Reflection.MethodInfo GetSizeOfMainGameView = T.GetMethod("GetSizeOfMainGameView", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Static);
        System.Object Res = GetSizeOfMainGameView.Invoke(null, null);
        return (Vector2)Res;
    }
}
