﻿using UnityEngine;
using System.Collections;
using System.IO;
using System;
using UnityEngine.UI;
using UnityEngine.Networking;

public class ImageDownloader : MonoBehaviour {

	// Use this for initialization
	void Start () {
//		StartCoroutine (download (""));
	}
	
	// Update is called once per frame
	void Update () {
	
	}

    public static IEnumerator DownloadImageCor(string imageUrl, string localFilePath, Action<Texture2D> success, Action failure, bool allowCache = true)
    {
		if (File.Exists(localFilePath) && allowCache)
		{
            var loading = ApplicationStorageFactory.Instance.DeviceStorage.ReadBytes(localFilePath);
            yield return loading;
            var data = loading.Current;

            var texture = new Texture2D(1, 1);
            texture.LoadImage(data);
            texture.Apply();
            success?.Invoke(texture);
		}
		else
		{
            var request = UnityWebRequestTexture.GetTexture(imageUrl);
            yield return request.SendWebRequest();
            if (request.result == UnityWebRequest.Result.ConnectionError)
            {
                DebugLogger.Log(request.error);
                failure?.Invoke();
            }
            else
            {
                DebugLogger.Log($"Download {imageUrl} succes");
                var texture = new Texture2D(1, 1);
                var data = request.downloadHandler.data;
                texture.LoadImage(data);
                texture.Apply();
                if (!string.IsNullOrEmpty(localFilePath))
                {
                    yield return ApplicationStorageFactory.Instance.DeviceStorage.Write(localFilePath, data);
                }
                success?.Invoke(texture);
            }
		}
    }

    public static IEnumerator DownloadImageCor(string imageUrl, string localFilePath, Action<Sprite> success, Action failure, bool allowCache = true)
    {
        yield return DownloadImageCor(imageUrl, localFilePath, (texture) =>
		{
			var sprite = Sprite.Create(texture, new Rect(0, 0, texture.width, texture.height), new Vector2(0.5f, 0.5f));
			success?.Invoke(sprite);
		}, failure, allowCache);
    }

	public static void LoadImgFromURL (string imgURL, Action<Texture> callback)
	{
		// Need to use a Coroutine for the WWW call, using Coroutiner convenience class
		Coroutiner.StartCoroutine(
			LoadImgEnumerator(imgURL, callback)
		);
	}

	static IEnumerator LoadImgEnumerator (string imgURL, Action<Texture> callback)
	{
		WWW www = new WWW(imgURL);
		yield return www;

		if (www.error != null)
		{
			Debug.LogError(www.error);
			yield break;
		}
		callback(www.texture);
	}

	public static WWW download(string url,string imagePath,string type){
		Debug.Log ("Downloading...");

		// Start a download of the given URL
		WWW www = new WWW(url);
		Coroutiner.StartCoroutine(
			downloadEnumerator(www, imagePath,type)
		);
		return www;
	}

    public static WWW download(string url, string imagePath, string type, Action<bool> callback, bool isSaveToDiscInGameScene = true){
		Debug.Log ("Downloading...");
		Debug.Log ("URL :: " + url);

		// Start a download of the given URL
		WWW www = new WWW(url);
		Coroutiner.StartCoroutine(
            downloadEnumerator(www, imagePath, type, callback, isSaveToDiscInGameScene)
		);
		return www;
	}

	static IEnumerator downloadEnumerator(WWW www,string imagePath,string type) {
		yield return www;

		if (www.error != null)
		{
			Debug.LogError(www.error);
			yield break;
		}
		string fileName = Application.persistentDataPath + "/" + imagePath; 
		if (isFileExist (fileName)) {
			Debug.Log ("File exists :: " + fileName);
		} else {
			if (type == "image") {
				Texture2D tex = www.texture;
				var bytes = tex.EncodeToPNG ();
				Destroy (tex);
				#if !UNITY_WEBPLAYER
				File.WriteAllBytes(fileName, bytes);
				#endif
			}
			if (type == "json") {
				#if !UNITY_WEBPLAYER
				File.WriteAllBytes(fileName, www.bytes);
				#endif
			}
			Debug.Log ("Downloaded finish" + fileName);
		}
	}

    static IEnumerator downloadEnumerator(WWW www,string imagePath,string type, Action<bool> callback,bool isSaveToDiscInGameScene = true) {
		yield return www;

		if (www.error != null)
		{
			Debug.LogError(www.error);
			yield break;
		}
        Debug.Log ("Download 100%, save image to disc ...");

		string fileName = Application.persistentDataPath + "/" + imagePath; 
        WaitForSeconds wait = new WaitForSeconds (1);

		if (type == "image") {
			Texture2D tex = www.texture;
			var bytes = tex.EncodeToPNG ();
			Destroy (tex);
			#if !UNITY_WEBPLAYER
			File.WriteAllBytes(fileName, bytes);
			#endif
		}
		if (type == "json") {
			#if !UNITY_WEBPLAYER
			File.WriteAllBytes(fileName, www.bytes);
			#endif
		}

		if (callback != null) {
			callback (true);
		}

		Debug.Log ("Downloaded finish" + fileName);
	}

	public static bool loadImage(RawImage target, string imageName){
		var bytes = new byte[0];
		try{
						#if !UNITY_WEBPLAYER
			bytes = File.ReadAllBytes (Application.persistentDataPath + imageName);
			#endif
		}
		catch(Exception e){
			Debug.Log (e.ToString ());
			return false;
		}
		if (bytes == null || bytes.Length == 0)
			return false;
		Texture2D tex2d = new Texture2D (2, 2);
		bool isSuccess = tex2d.LoadImage (bytes);
		if (isSuccess)
			target.texture = tex2d;
		return isSuccess;
	}

	public static Sprite loadSprite(string imageName){
		Sprite sprite = null;
		var bytes = new byte[0];
		try{
						#if !UNITY_WEBPLAYER
			bytes = File.ReadAllBytes (Application.persistentDataPath+ "/" + imageName);
			#endif
		}
		catch(Exception e){
			Debug.Log (e.ToString ());
			return sprite;
		}
		if (bytes == null || bytes.Length == 0)
			return sprite;
		Texture2D tex2d = new Texture2D (2, 2);
		tex2d.LoadImage (bytes);
		sprite = Sprite.Create(tex2d, new Rect(0,0,tex2d.width, tex2d.height), new Vector2(0.5f,0.5f));
		return sprite;
	}

	public static string loadText(string name){
		string bytes ="";
		try{
			#if !UNITY_WEBPLAYER
			bytes = File.ReadAllText (Application.persistentDataPath+ "/" + name);
			#endif
		}
		catch(Exception e){
			Debug.Log (e.ToString ());
		}
		return bytes;
	}

	public static bool writeText(string filePath, string text) {
		string fileName = Application.persistentDataPath + "/" + filePath;
		#if !UNITY_WEBPLAYER
		File.WriteAllText(fileName, text);
		#endif
		return true;
	}

	public static bool isFileExist(string fileName){
		return File.Exists (Application.persistentDataPath+ "/" + fileName);
	}

}
