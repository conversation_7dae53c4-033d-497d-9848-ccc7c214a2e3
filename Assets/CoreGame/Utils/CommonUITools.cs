﻿using System;
using System.Collections;
using UnityEngine;
using UnityEngine.UI;

/// <summary>
/// Common UI tools.
/// </summary>
public class CommonUITools
{
    public static GameObject SpawnPrefab(GameObject _prefab, Transform _spawnParent)
    {
        GameObject obj = SpawnPrefab(_prefab, Vector3.zero, Vector3.one, _spawnParent);
        return obj;
    }

    public static T SpawnPrefab<T>(GameObject _prefab, Transform _spawnParent, bool _worldPositionStays = true) where T : Component
    {
        return SpawnPrefab<T>(_prefab, Vector3.zero, Vector3.one, _spawnParent, _worldPositionStays);
    }

    public static T SpawnPrefab<T>(GameObject _prefab, Vector3 _spawnPos, Transform _spawnParent, bool _worldPositionStays = true) where T : Component
    {
        return SpawnPrefab<T>(_prefab, _spawnPos, Vector3.one, _spawnParent, _worldPositionStays);
    }

    public static GameObject SpawnPrefab(GameObject _prefab, Vector3 _spawnPos, Vector3 _scale, Transform _spawnParent, bool _worldPositionStays = true)
    {
        GameObject obj =  UnityEngine.Object.Instantiate(_prefab, Vector3.zero, Quaternion.identity) as GameObject;
        return SetInitProperties(obj, _spawnPos, _scale, _spawnParent, _worldPositionStays);
    }

    public static GameObject SetInitProperties(GameObject _obj, Vector3 _spawnPos, Vector3 _scale, Transform _spawnParent, bool _worldPositionStays = true)
    {
        Transform trans = _obj.transform;
        trans.SetParent(_spawnParent, _worldPositionStays);
        trans.localScale = _scale;

        RectTransform rectTrans = trans as RectTransform;

        if (rectTrans != null)
        {
            rectTrans.anchoredPosition3D = _spawnPos;
            if (rectTrans.anchorMax == Vector2.one && rectTrans.anchorMin == Vector2.zero)
            {
                rectTrans.sizeDelta = Vector2.zero;
            }
        }
        else
        {
            trans.localPosition = _spawnPos;
        }

        return _obj;
    }

    public static T SpawnPrefab<T>(GameObject _prefab, Vector3 _spawnPos, Vector3 _scale, Transform _spawnParent, bool _worldPositionStays = true) where T : Component
    {
        GameObject obj = SpawnPrefab(_prefab, _spawnPos, _scale, _spawnParent, _worldPositionStays);

        return obj.GetComponent<T>();
    }

    /// <summary>
    /// Image Load
    /// </summary>
    /// <param name="_rawImage"></param>
    /// <param name="_imagePath"></param>
    /// <returns></returns>
    public static IEnumerator ImageLoad(RawImage _rawImage, string _imagePath, Action _completeCb = null, Action _failCb = null, bool showLoading = false)
    {
        string url = _imagePath;
        WWW www = new WWW(url);
        if (showLoading)
            AlphaLoading.Instance.StartLoading();
        yield return www;
        if (showLoading)
            AlphaLoading.Instance.EndLoading();
        if (string.IsNullOrEmpty(www.error))
        {
            int texHeight = www.textureNonReadable.height;
            int texWidth = www.textureNonReadable.width;
            Texture2D texture = new Texture2D(www.textureNonReadable.width, www.textureNonReadable.height, TextureFormat.ARGB32, false);
            www.LoadImageIntoTexture(texture);
            _rawImage.texture = texture;
            LayoutElement element = _rawImage.gameObject.GetComponent<LayoutElement>();
            RectTransform trans = _rawImage.gameObject.GetComponent<RectTransform>();
            float imageWidth = trans.sizeDelta.x;
            // 横幅比で高さ調整
            float widthRatio = imageWidth / texWidth;
            element.preferredHeight = texHeight * widthRatio;
            BaseUtils.ExcuteAction(_completeCb);
        }
        else
        {
#if DEBUG
            Debug.LogError("ImageLoad: " + url + " Error:" + www.error);
#endif
            BaseUtils.ExcuteAction(_failCb);
        }
    }

    public static void SetSizeKeepAspect(Image image){
        if(image.sprite == null) return;
        if(image.sprite.bounds.size.x / image.sprite.bounds.size.y != 0 )
        image.GetComponent<RectTransform>().sizeDelta = new Vector2(image.GetComponent<RectTransform>().sizeDelta.x, image.GetComponent<RectTransform>().sizeDelta.x * image.sprite.bounds.size.y / image.sprite.bounds.size.x);
    }
}
