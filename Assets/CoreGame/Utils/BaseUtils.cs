using System.Collections.Generic;
using System.Collections;
using System;

using UnityEngine;
using System.Reflection;
using System.IO;
using System.Linq;

public static class BaseUtils
{
    #region Prefabs

    private static Dictionary<string, GameObject> preloadedPrefabs = new Dictionary<string, GameObject>();

    public static void UnloadAllPrefabs()
    {
        preloadedPrefabs.Clear();
    }

    public static void PreloadPrefab(string path)
    {
        if (!preloadedPrefabs.ContainsKey(path))
        {
            preloadedPrefabs.Add(path, Resources.Load(path) as GameObject);
        }
    }

    public static GameObject Instantiate(string path, Vector3 position = default(Vector3), Quaternion rotation = default(Quaternion))
    {
        return UnityEngine.Object.Instantiate(BaseUtils.LoadPrefab(path), position, rotation) as GameObject;
    }

    public static GameObject LoadPrefab(string path)
    {
        return preloadedPrefabs.ContainsKey(path) ? preloadedPrefabs[path] : Resources.Load(path) as GameObject;
    }
    #endregion

    #region Date
    public static DateTime EpochDate = new DateTime(1970, 1, 1);

    public static long UnixTimestampToday(DateTime date)
    {
        return (long)((new DateTime(date.Year, date.Month, date.Day, 0, 0, 0, 0)).Subtract(EpochDate).TotalSeconds);
    }

    public static long ToUnixTimestamp(DateTime date)
    {
        return (long)(date.Subtract(EpochDate).TotalSeconds);
    }

    public static DateTime UnixTimeStampToDateTime(double unixTimeStamp)
    {
        System.DateTime date = new DateTime(1970, 1, 1, 0, 0, 0, 0, System.DateTimeKind.Utc);
        date = date.AddSeconds(unixTimeStamp);
        return date;
    }

    public static double DateTimetoUnixTimeStamp(DateTime dateTime)
    {
        return (dateTime - new DateTime(1970, 1, 1, 0, 0, 0, 0)).TotalSeconds;
    }

    public static string GetTimeYYYYMMDD (string time)
    {
        return ToDateTime(time).ToString ("yyyy/MM/dd");
    }

    public static string GetTimeYYYYMMDDFormat (string time)
    {
        return ToDateTime(time).ToString ("yyyy年MM月dd日");
    }

    public static string GetMMDDTimeFormat (string time)
    {
        return ToDateTime(time).ToString ("MM月dd日");
    }

    public static string GetMDTimeFormat (string time)
    {
        return ToDateTime(time).ToString ("M月d日");
    }

    public static string GetMMDDHHmmTimeFormat (string time)
    {
        return ToDateTime(time).ToString ("MM月dd日HH:mm");
    }

    public static string GetMDHmmTimeFormat (string time)
    {
        return ToDateTime(time).ToString ("M月d日H:mm");
    }

    public static DateTime ToDateTime(string time)
    {
        DateTime date;
        if (!DateTime.TryParse(time, System.Globalization.CultureInfo.InvariantCulture, System.Globalization.DateTimeStyles.None, out date))
        {
            Debug.LogError("Can't convert to DateTime from string: " + time);
            return DateTime.MinValue; // Return a default value instead of potentially invalid date
        }
        return date;
    }
    #endregion

    #region string
    public static string ConvertNumToCommasInThousand(int _num)
    {
        return string.Format("{0:n0}", _num);
    }
    public static string ConvertNumToCommasInThousand(long _num)
    {
        return string.Format("{0:n0}", _num);
    }
    #endregion

    public static GameObject GetChildRecursive(GameObject go, string name)
    {
        Component[] transforms = go.GetComponentsInChildren(typeof(Transform), true);

        foreach (Transform transform in transforms)
        {
            if (transform.gameObject.name == name)
            {
                return transform.gameObject;
            }
        }

        return null;
    }

    public static T GetChildRecursive<T>(GameObject go) where T : Component
    {
        Component[] comps = go.GetComponentsInChildren(typeof(T), true);

        if (comps.Length > 0)
        {
            return (T)comps[0];
        }

        return null;
    }

    public static string GetFilenameWithoutExtension(string path)
    {
        int indexStart = path.LastIndexOf('/') + 1;
        int indexEnd = path.LastIndexOf('.');

        if (indexEnd < 0)
        {
            indexEnd = path.Length;
        }

        if (indexStart > 0 && indexEnd >= 0)
        {
            return path.Substring(indexStart, indexEnd - indexStart);
        }
        else
        {
            return path.Substring(0, indexEnd);
        }
    }

    public static string GetDirname(string path)
    {
        int index = path.LastIndexOf('/');

        if (index >= 0)
        {
            return path.Substring(0, index);
        }
        else
        {
            return path;
        }
    }

    public static string ReverseString(string s)
    {
        char[] arr = s.ToCharArray();
        Array.Reverse(arr);
        return new string(arr);
    }

    public static void ResetGOForCanvas(GameObject go)
    {
        go.transform.localScale = Vector3.one;
        go.transform.localPosition = Vector3.zero;
        RectTransform rectTransform = go.GetComponent<RectTransform>();
        rectTransform.offsetMin = Vector2.zero;
        rectTransform.offsetMax = Vector2.zero;
    }

    public static bool InternetStatus()
    {
        return Application.internetReachability != NetworkReachability.NotReachable ? true : false;
    }

    public static void ExcuteAction(Action action)
    {
        if (action != null)
            action();
    }

	public static void ExcuteAction(Action<string> action,string _msg) {
		if (action != null)
			action(_msg);
	}

	public static IEnumerator ExcuteActionAfterTime(Action action, float time)
    {
        yield return new WaitForSeconds(time);
        ExcuteAction(action);
    }

    public static IEnumerator ExcuteActionNextFrame(Action action)
    {
        yield return null;
        ExcuteAction(action);
    }

    public static string GetNumberWithSymbol(int number){
        if (number > 0)
            return "+" + number;
        return number.ToString ();
    }

    public static void CopyClassValues<T>(T sourceComp, T targetComp)
    {
        FieldInfo[] sourceFields = sourceComp.GetType().GetFields(BindingFlags.Public | BindingFlags.Instance);
        int i = 0;
        for (i = 0; i < sourceFields.Length; i++)
        {
            var value = sourceFields[i].GetValue(sourceComp);
            sourceFields[i].SetValue(targetComp, value);
        }
    }

    public static string GetGitBranch()
    {
        try{

            var appPath = Application.dataPath.Substring(0, Application.dataPath.Length - 6);
            var headFilePath = appPath + ".git/HEAD";
            var refFilePath = appPath + ".git/packed-refs";
            var headFile = File.ReadAllText(headFilePath);
            var branchLine = headFile.Split('\n')[0].Substring(16);
            Debug.Log(branchLine);
            // var branchLine = refFile.Split('\n').ToList().FirstOrDefault(x => x.Contains(commit) && x.Contains("heads"));
            return branchLine.Replace('/', '.').Replace('\\', '.');
        }
        catch (Exception)
        {
        }
        return "default.branch";
    }

    #region enum
    public static T ToEnum<T>(this string value, T defaultValue) where T : struct, Enum
    {
        if (string.IsNullOrEmpty(value))
        {
            return defaultValue;
        }

        T result;
        return Enum.TryParse<T>(value, true, out result) ? result : defaultValue;
    }
    #endregion
}