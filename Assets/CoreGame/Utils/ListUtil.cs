﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;

public class ListUtil
{
	/// <summary>
	/// Used in Shuffle(T).
	/// </summary>
	static Random _random = new Random();

	/// <summary>
	/// </summary>
	/// <typeparam name="T">Array element type.</typeparam>
	/// <param name="array">Array to shuffle.</param>
	public static void Shuffle<T> (List<T> array, Random designatedRandom = null)
	{
		Random random = designatedRandom != null ? designatedRandom : _random;

		for (int i = array.Count; i > 1; i--)
		{
			// Pick random element to swap.
			int j = random.Next(i);  // 0 <= j <= i-1
			// Swap.
			T tmp = array [j];
			array [j] = array [i - 1];
			array [i - 1] = tmp;
		}
	}

	public static T RandomElementAt<T>(IEnumerable<T> ie, Random designatedRandom = null)
	{
		Random random = designatedRandom != null ? designatedRandom : _random;
		return ie.ElementAt(random.Next(ie.Count()));
	}

	public static IEnumerable<T> GetRandomElements<T>(IEnumerable<T> ie, int numElement, Random designatedRandom = null)
	{
		var listRand = ie.ToList();
		Shuffle(listRand, designatedRandom);
		numElement = Math.Min(numElement, listRand.Count);
        return listRand.GetRange(0, numElement);
	}

	public static bool IsNullOrEmpty(IList list)
	{
		return list == null || list.Count == 0;
	}
}
