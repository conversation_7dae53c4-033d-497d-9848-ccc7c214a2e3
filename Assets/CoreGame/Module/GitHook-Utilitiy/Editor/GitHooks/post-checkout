#!/bin/sh
command -v git-lfs >/dev/null 2>&1 || { echo >&2 "\nThis repository is configured for Git LFS but 'git-lfs' was not found on your path. If you no longer wish to use Git LFS, remove this hook by deleting .git/hooks/post-checkout.\n"; exit 2; }
git lfs post-checkout "$@"

find Assets -depth \
    \( -type f -name ".DS_Store" -delete \) -or \
    \( -type f -name "Thumbs.db" -delete \) -or \
    \( -type f -name "Desktop.ini" -delete \) -or \
    \( -type f -name "desktop.ini" -delete \) -or \
    \( -type d -empty -exec echo "Removing: {}" \; -delete -exec rm -f "{}.meta" \; \)