%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0540c721966034abeac33cd5f4007f90, type: 3}
  m_Name: SongData-breezy-fire-in-the-hole
  m_EditorClassIdentifier: 
  listSong:
  - name: "\u26100 - 11MB - Breezy plains - BREEZY UPDATE 2 - airdetected -  - air-detected
      - air-detected"
    modId: air-detected
    modName: air-detected
    songName: Breezy plains
    singer: BREEZY UPDATE 2
    id: air-detected_breezy-plains
    fileId: 
    speedMultipleEasy: 0.7
    speedMultiple: 0.9
    speedMultipleHard: 1.1
    speedMultipleHell: 1
    hasVocal: 1
    week: 0
    weekId: airdetected
    myCharacterIcon: {fileID: 21300000, guid: ae5a3c1a931224f79a1bbbb80864a797, type: 3}
    opCharacterIcon: {fileID: 21300000, guid: ec968d2c29d614f2cb544da859e68136, type: 3}
    modIcon: {fileID: 0}
    isDataSwap: 0
    isSelectBackground: 1
    isIncludeInBuild: 0
    isFastFollow: 0
    isInstallTime: 0
    isMappingNote: 0
    hasNoteUpMode: 1
    isFreePlayOnly: 0
    startTime: 0
    endTime: 0
    isConvertData: 1
    numCoinBonusEasy: -1
    numCoinBonusNormal: -1
    numCoinBonusHard: -1
    numCoinBonusHell: -1
    unlockType: 1
    numCoinUnlock: -1
    gameModeSkip: 
    songType: 0
    songAdsUrl: 
    bfIconAssetUrl: https://pub-d6f5eb52ee3645eea1acef1337e865e5.r2.dev/stages/icons/1.0/com.ChinChinStd.FridayNight/Android/Assets/FNF/SongData/Icons/NF_Icons_NF_air-detected_images_icons_icon-gf-new_bf_skip_NF_air-detected_images_icons_icon-gf-new_bf_skip_skip.png
    opIconAssetUrl: https://pub-d6f5eb52ee3645eea1acef1337e865e5.r2.dev/stages/icons/1.0/com.ChinChinStd.FridayNight/Android/Assets/FNF/SongData/Icons/NF_Icons_NF_air-detected_images_icons_icon-insane-face_op_skip_NF_air-detected_images_icons_icon-insane-face_op_skip_skip.png
    musicPreviewAssetUrl: 
    musicPreviewAssetBackupUrl: 
    songUrlCollection:
      songUrls:
      - difficultMode: 0
        assetUrl: 
        backupAssetUrl: 
      - difficultMode: 1
        assetUrl: https://pub-d6f5eb52ee3645eea1acef1337e865e5.r2.dev/stages/1.0/com.ChinChinStd.FridayNight/Android/air-detected_breezy-plains
        backupAssetUrl: 
      - difficultMode: 2
        assetUrl: 
        backupAssetUrl: 
      - difficultMode: 3
        assetUrl: 
        backupAssetUrl: 
    StartAnimationPrefabId: 
    bundleFileSize: 11
    createdDate: 2024-05-22 10:37:05
    specialMaxPPBonus: 0
    maxPP: 225
    bpm: 0
  - name: "\u26100 - 8MB - Burning landscapes - BREEZY UPDATE - airdetected -  -
      air-detected - air-detected"
    modId: air-detected
    modName: air-detected
    songName: Burning landscapes
    singer: BREEZY UPDATE
    id: air-detected_burning-landscapes
    fileId: 
    speedMultipleEasy: 0.7
    speedMultiple: 0.9
    speedMultipleHard: 1.1
    speedMultipleHell: 1
    hasVocal: 1
    week: 0
    weekId: airdetected
    myCharacterIcon: {fileID: 21300000, guid: d24d3baad600a4381a9abca4a6d9cdbc, type: 3}
    opCharacterIcon: {fileID: 21300000, guid: 7da079aeaf3f24e9db0d7e589bdb8ef0, type: 3}
    modIcon: {fileID: 0}
    isDataSwap: 0
    isSelectBackground: 1
    isIncludeInBuild: 0
    isFastFollow: 0
    isInstallTime: 0
    isMappingNote: 0
    hasNoteUpMode: 1
    isFreePlayOnly: 0
    startTime: 0
    endTime: 0
    isConvertData: 1
    numCoinBonusEasy: -1
    numCoinBonusNormal: -1
    numCoinBonusHard: -1
    numCoinBonusHell: -1
    unlockType: 1
    numCoinUnlock: -1
    gameModeSkip: 
    songType: 0
    songAdsUrl: 
    bfIconAssetUrl: https://pub-d6f5eb52ee3645eea1acef1337e865e5.r2.dev/stages/icons/1.0/com.ChinChinStd.FridayNight/Android/Assets/FNF/SongData/Icons/NF_Icons_NF_air-detected_images_icons_icon-insane-chase_bf_skip_NF_air-detected_images_icons_icon-insane-chase_bf_skip_skip.png
    opIconAssetUrl: https://pub-d6f5eb52ee3645eea1acef1337e865e5.r2.dev/stages/icons/1.0/com.ChinChinStd.FridayNight/Android/Assets/FNF/SongData/Icons/NF_Icons_NF_air-detected_images_icons_icon-normal-chase_op_skip_NF_air-detected_images_icons_icon-normal-chase_op_skip_skip.png
    musicPreviewAssetUrl: 
    musicPreviewAssetBackupUrl: 
    songUrlCollection:
      songUrls:
      - difficultMode: 0
        assetUrl: 
        backupAssetUrl: 
      - difficultMode: 1
        assetUrl: https://pub-d6f5eb52ee3645eea1acef1337e865e5.r2.dev/stages/1.0/com.ChinChinStd.FridayNight/Android/air-detected_burning-landscapes
        backupAssetUrl: 
      - difficultMode: 2
        assetUrl: 
        backupAssetUrl: 
      - difficultMode: 3
        assetUrl: 
        backupAssetUrl: 
    StartAnimationPrefabId: 
    bundleFileSize: 8
    createdDate: 2024-05-22 10:37:11
    specialMaxPPBonus: 0
    maxPP: 262
    bpm: 0
  - name: "\u26100 - 4MB - Detection - BREEZY UPDATE - airdetected -  - air-detected
      - air-detected"
    modId: air-detected
    modName: air-detected
    songName: Detection
    singer: BREEZY UPDATE
    id: air-detected_detection
    fileId: 
    speedMultipleEasy: 0.7
    speedMultiple: 0.9
    speedMultipleHard: 1.1
    speedMultipleHell: 1
    hasVocal: 1
    week: 0
    weekId: airdetected
    myCharacterIcon: {fileID: 21300000, guid: 5f068cf5ba19246d6bfd0f7c32aaf074, type: 3}
    opCharacterIcon: {fileID: 21300000, guid: a1cfa8239ce684b7d83b6785cb92961c, type: 3}
    modIcon: {fileID: 0}
    isDataSwap: 0
    isSelectBackground: 1
    isIncludeInBuild: 0
    isFastFollow: 0
    isInstallTime: 0
    isMappingNote: 0
    hasNoteUpMode: 1
    isFreePlayOnly: 0
    startTime: 0
    endTime: 0
    isConvertData: 1
    numCoinBonusEasy: -1
    numCoinBonusNormal: -1
    numCoinBonusHard: -1
    numCoinBonusHell: -1
    unlockType: 1
    numCoinUnlock: -1
    gameModeSkip: 
    songType: 0
    songAdsUrl: 
    bfIconAssetUrl: https://pub-d6f5eb52ee3645eea1acef1337e865e5.r2.dev/stages/icons/1.0/com.ChinChinStd.FridayNight/Android/Assets/FNF/SongData/Icons/NF_Icons_NF_air-detected_images_icons_icon-auto-face_bf_skip_NF_air-detected_images_icons_icon-auto-face_bf_skip_skip.png
    opIconAssetUrl: https://pub-d6f5eb52ee3645eea1acef1337e865e5.r2.dev/stages/icons/1.0/com.ChinChinStd.FridayNight/Android/Assets/FNF/SongData/Icons/NF_Icons_NF_air-detected_images_icons_icon-normal-face_op_skip_NF_air-detected_images_icons_icon-normal-face_op_skip_skip.png
    musicPreviewAssetUrl: 
    musicPreviewAssetBackupUrl: 
    songUrlCollection:
      songUrls:
      - difficultMode: 0
        assetUrl: 
        backupAssetUrl: 
      - difficultMode: 1
        assetUrl: https://pub-d6f5eb52ee3645eea1acef1337e865e5.r2.dev/stages/1.0/com.ChinChinStd.FridayNight/Android/air-detected_detection
        backupAssetUrl: 
      - difficultMode: 2
        assetUrl: 
        backupAssetUrl: 
      - difficultMode: 3
        assetUrl: 
        backupAssetUrl: 
    StartAnimationPrefabId: 
    bundleFileSize: 4
    createdDate: 2024-05-22 10:37:17
    specialMaxPPBonus: 0
    maxPP: 175
    bpm: 0
  tempSong:
    name: "\u26101 - 11MB - Breezy plains - BREEZY UPDATE - airdetected -  - air-detected
      - air-detected"
    modId: air-detected
    modName: air-detected
    songName: Breezy plains
    singer: BREEZY UPDATE
    id: air-detected_breezy-plains
    fileId: 
    speedMultipleEasy: 0.7
    speedMultiple: 0.9
    speedMultipleHard: 1.1
    speedMultipleHell: 1
    hasVocal: 1
    week: 1
    weekId: airdetected
    myCharacterIcon: {fileID: 21300000, guid: ae5a3c1a931224f79a1bbbb80864a797, type: 3}
    opCharacterIcon: {fileID: 21300000, guid: ec968d2c29d614f2cb544da859e68136, type: 3}
    modIcon: {fileID: 0}
    isDataSwap: 0
    isSelectBackground: 1
    isIncludeInBuild: 0
    isFastFollow: 0
    isInstallTime: 0
    isMappingNote: 0
    hasNoteUpMode: 1
    isFreePlayOnly: 0
    startTime: 0
    endTime: 0
    isConvertData: 1
    numCoinBonusEasy: -1
    numCoinBonusNormal: -1
    numCoinBonusHard: -1
    numCoinBonusHell: -1
    unlockType: 1
    numCoinUnlock: -1
    gameModeSkip: 
    songType: 0
    songAdsUrl: 
    bfIconAssetUrl: https://pub-d6f5eb52ee3645eea1acef1337e865e5.r2.dev/stages/icons/1.0/com.ChinChinStd.FridayNight/Android/Assets/FNF/SongData/Icons/NF_Icons_NF_air-detected_images_icons_icon-gf-new_bf_skip_NF_air-detected_images_icons_icon-gf-new_bf_skip_skip.png
    opIconAssetUrl: https://pub-d6f5eb52ee3645eea1acef1337e865e5.r2.dev/stages/icons/1.0/com.ChinChinStd.FridayNight/Android/Assets/FNF/SongData/Icons/NF_Icons_NF_air-detected_images_icons_icon-insane-face_op_skip_NF_air-detected_images_icons_icon-insane-face_op_skip_skip.png
    musicPreviewAssetUrl: 
    musicPreviewAssetBackupUrl: 
    songUrlCollection:
      songUrls:
      - difficultMode: 0
        assetUrl: 
        backupAssetUrl: 
      - difficultMode: 1
        assetUrl: https://pub-d6f5eb52ee3645eea1acef1337e865e5.r2.dev/stages/1.0/com.ChinChinStd.FridayNight/Android/air-detected_breezy-plains
        backupAssetUrl: 
      - difficultMode: 2
        assetUrl: 
        backupAssetUrl: 
      - difficultMode: 3
        assetUrl: 
        backupAssetUrl: 
    StartAnimationPrefabId: 
    bundleFileSize: 11
    createdDate: 2024-05-22 10:37:05
    specialMaxPPBonus: 0
    maxPP: 225
    bpm: 0
  eventData:
    eventId: 
    eventName: 
    listEventReward: []
    listWeekSetting: []
  listNewSongsID: []
  listHotSongsID: []
  weeksRelationships: []
  modInfos: []
  cloudSongDataUrl: https://pub-d6f5eb52ee3645eea1acef1337e865e5.r2.dev/SongData/1.0/com.ChinChinStd.FridayNight/Android
  cloudSongDataBackupUrl: 
  cloudSongDataGamePackageId: 
  cloudSongDataGameVersion: 
  bundleFileSize: 2.4
  buildVersion: 0
  numSongBuild: 0
  localAdsBanners: []
  songDataVersion: 4.0
  eventDataMasters: []
  shopData: {fileID: 0}
