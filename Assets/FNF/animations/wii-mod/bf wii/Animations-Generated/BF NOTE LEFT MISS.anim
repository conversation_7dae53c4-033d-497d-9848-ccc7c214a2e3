%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!74 &7400000
AnimationClip:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: BF NOTE LEFT MISS
  serializedVersion: 6
  m_Legacy: 0
  m_Compressed: 0
  m_UseHighQualityCurve: 1
  m_RotationCurves: []
  m_CompressedRotationCurves: []
  m_EulerCurves: []
  m_PositionCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: {x: -0.27, y: 0, z: 0}
        inSlope: {x: Infinity, y: 0, z: 0}
        outSlope: {x: Infinity, y: 0, z: 0}
        tangentMode: 0
        weightedMode: 0
        inWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
        outWeight: {x: 0.33333334, y: 0.33333334, z: 0.33333334}
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    path: sprite
  m_ScaleCurves: []
  m_FloatCurves: []
  m_PPtrCurves:
  - curve:
    - time: 0
      value: {fileID: -38283081728239499, guid: 522efc9afd2e44ed4a1604a36658f0ba,
        type: 3}
    - time: 0.041666668
      value: {fileID: 4117713862470842086, guid: 522efc9afd2e44ed4a1604a36658f0ba,
        type: 3}
    - time: 0.083333336
      value: {fileID: -8022964114395799130, guid: 522efc9afd2e44ed4a1604a36658f0ba,
        type: 3}
    - time: 0.125
      value: {fileID: 135115286792928563, guid: 522efc9afd2e44ed4a1604a36658f0ba,
        type: 3}
    - time: 0.16666667
      value: {fileID: -3934107588648591103, guid: 522efc9afd2e44ed4a1604a36658f0ba,
        type: 3}
    - time: 0.20833333
      value: {fileID: 548465649199281453, guid: 522efc9afd2e44ed4a1604a36658f0ba,
        type: 3}
    - time: 0.25
      value: {fileID: -7065001023849083533, guid: 522efc9afd2e44ed4a1604a36658f0ba,
        type: 3}
    - time: 0.29166666
      value: {fileID: 5820440426651726594, guid: 522efc9afd2e44ed4a1604a36658f0ba,
        type: 3}
    - time: 0.33333334
      value: {fileID: -7583743579677315856, guid: 522efc9afd2e44ed4a1604a36658f0ba,
        type: 3}
    - time: 0.375
      value: {fileID: 5883972654985425771, guid: 522efc9afd2e44ed4a1604a36658f0ba,
        type: 3}
    - time: 0.41666666
      value: {fileID: 1188081106089255490, guid: 522efc9afd2e44ed4a1604a36658f0ba,
        type: 3}
    - time: 0.45833334
      value: {fileID: -6697293422948780913, guid: 522efc9afd2e44ed4a1604a36658f0ba,
        type: 3}
    - time: 0.5
      value: {fileID: 6768590870528355731, guid: 522efc9afd2e44ed4a1604a36658f0ba,
        type: 3}
    attribute: m_Sprite
    path: sprite
    classID: 212
    script: {fileID: 0}
  m_SampleRate: 24
  m_WrapMode: 2
  m_Bounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  m_ClipBindingConstant:
    genericBindings:
    - serializedVersion: 2
      path: 891129758
      attribute: 1
      script: {fileID: 0}
      typeID: 4
      customType: 0
      isPPtrCurve: 0
    - serializedVersion: 2
      path: 891129758
      attribute: 0
      script: {fileID: 0}
      typeID: 212
      customType: 23
      isPPtrCurve: 1
    pptrCurveMapping:
    - {fileID: -38283081728239499, guid: 522efc9afd2e44ed4a1604a36658f0ba, type: 3}
    - {fileID: 4117713862470842086, guid: 522efc9afd2e44ed4a1604a36658f0ba, type: 3}
    - {fileID: -8022964114395799130, guid: 522efc9afd2e44ed4a1604a36658f0ba, type: 3}
    - {fileID: 135115286792928563, guid: 522efc9afd2e44ed4a1604a36658f0ba, type: 3}
    - {fileID: -3934107588648591103, guid: 522efc9afd2e44ed4a1604a36658f0ba, type: 3}
    - {fileID: 548465649199281453, guid: 522efc9afd2e44ed4a1604a36658f0ba, type: 3}
    - {fileID: -7065001023849083533, guid: 522efc9afd2e44ed4a1604a36658f0ba, type: 3}
    - {fileID: 5820440426651726594, guid: 522efc9afd2e44ed4a1604a36658f0ba, type: 3}
    - {fileID: -7583743579677315856, guid: 522efc9afd2e44ed4a1604a36658f0ba, type: 3}
    - {fileID: 5883972654985425771, guid: 522efc9afd2e44ed4a1604a36658f0ba, type: 3}
    - {fileID: 1188081106089255490, guid: 522efc9afd2e44ed4a1604a36658f0ba, type: 3}
    - {fileID: -6697293422948780913, guid: 522efc9afd2e44ed4a1604a36658f0ba, type: 3}
    - {fileID: 6768590870528355731, guid: 522efc9afd2e44ed4a1604a36658f0ba, type: 3}
  m_AnimationClipSettings:
    serializedVersion: 2
    m_AdditiveReferencePoseClip: {fileID: 0}
    m_AdditiveReferencePoseTime: 0
    m_StartTime: 0
    m_StopTime: 0.5416667
    m_OrientationOffsetY: 0
    m_Level: 0
    m_CycleOffset: 0
    m_HasAdditiveReferencePose: 0
    m_LoopTime: 1
    m_LoopBlend: 0
    m_LoopBlendOrientation: 0
    m_LoopBlendPositionY: 0
    m_LoopBlendPositionXZ: 0
    m_KeepOriginalOrientation: 0
    m_KeepOriginalPositionY: 1
    m_KeepOriginalPositionXZ: 0
    m_HeightFromFeet: 0
    m_Mirror: 0
  m_EditorCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: -0.27
        inSlope: Infinity
        outSlope: Infinity
        tangentMode: 103
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalPosition.x
    path: sprite
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: Infinity
        outSlope: Infinity
        tangentMode: 103
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalPosition.y
    path: sprite
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: Infinity
        outSlope: Infinity
        tangentMode: 103
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalPosition.z
    path: sprite
    classID: 4
    script: {fileID: 0}
  m_EulerEditorCurves: []
  m_HasGenericRootTransform: 0
  m_HasMotionFloatCurves: 0
  m_Events: []
