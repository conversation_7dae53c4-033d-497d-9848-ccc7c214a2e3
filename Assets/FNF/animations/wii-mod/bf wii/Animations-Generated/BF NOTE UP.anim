%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!74 &7400000
AnimationClip:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: BF NOTE UP
  serializedVersion: 6
  m_Legacy: 0
  m_Compressed: 0
  m_UseHighQualityCurve: 1
  m_RotationCurves: []
  m_CompressedRotationCurves: []
  m_EulerCurves: []
  m_PositionCurves: []
  m_ScaleCurves: []
  m_FloatCurves: []
  m_PPtrCurves:
  - curve:
    - time: 0
      value: {fileID: 3987713259247836068, guid: 522efc9afd2e44ed4a1604a36658f0ba,
        type: 3}
    - time: 0.041666668
      value: {fileID: -4810167972658097056, guid: 522efc9afd2e44ed4a1604a36658f0ba,
        type: 3}
    - time: 0.083333336
      value: {fileID: -8007130745871433923, guid: 522efc9afd2e44ed4a1604a36658f0ba,
        type: 3}
    - time: 0.125
      value: {fileID: -8285089215911767872, guid: 522efc9afd2e44ed4a1604a36658f0ba,
        type: 3}
    - time: 0.16666667
      value: {fileID: 2208699351942960641, guid: 522efc9afd2e44ed4a1604a36658f0ba,
        type: 3}
    - time: 0.20833333
      value: {fileID: 8091763342681797755, guid: 522efc9afd2e44ed4a1604a36658f0ba,
        type: 3}
    - time: 0.25
      value: {fileID: -4645129945003858986, guid: 522efc9afd2e44ed4a1604a36658f0ba,
        type: 3}
    - time: 0.29166666
      value: {fileID: -654238341739434413, guid: 522efc9afd2e44ed4a1604a36658f0ba,
        type: 3}
    - time: 0.33333334
      value: {fileID: 3124294255010252692, guid: 522efc9afd2e44ed4a1604a36658f0ba,
        type: 3}
    - time: 0.375
      value: {fileID: 6577736242046176163, guid: 522efc9afd2e44ed4a1604a36658f0ba,
        type: 3}
    - time: 0.41666666
      value: {fileID: 208435388225878645, guid: 522efc9afd2e44ed4a1604a36658f0ba,
        type: 3}
    - time: 0.45833334
      value: {fileID: -5810008528621116471, guid: 522efc9afd2e44ed4a1604a36658f0ba,
        type: 3}
    - time: 0.5
      value: {fileID: -9172249567589604163, guid: 522efc9afd2e44ed4a1604a36658f0ba,
        type: 3}
    attribute: m_Sprite
    path: sprite
    classID: 212
    script: {fileID: 0}
  m_SampleRate: 24
  m_WrapMode: 2
  m_Bounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  m_ClipBindingConstant:
    genericBindings:
    - serializedVersion: 2
      path: 891129758
      attribute: 0
      script: {fileID: 0}
      typeID: 212
      customType: 23
      isPPtrCurve: 1
    pptrCurveMapping:
    - {fileID: 3987713259247836068, guid: 522efc9afd2e44ed4a1604a36658f0ba, type: 3}
    - {fileID: -4810167972658097056, guid: 522efc9afd2e44ed4a1604a36658f0ba, type: 3}
    - {fileID: -8007130745871433923, guid: 522efc9afd2e44ed4a1604a36658f0ba, type: 3}
    - {fileID: -8285089215911767872, guid: 522efc9afd2e44ed4a1604a36658f0ba, type: 3}
    - {fileID: 2208699351942960641, guid: 522efc9afd2e44ed4a1604a36658f0ba, type: 3}
    - {fileID: 8091763342681797755, guid: 522efc9afd2e44ed4a1604a36658f0ba, type: 3}
    - {fileID: -4645129945003858986, guid: 522efc9afd2e44ed4a1604a36658f0ba, type: 3}
    - {fileID: -654238341739434413, guid: 522efc9afd2e44ed4a1604a36658f0ba, type: 3}
    - {fileID: 3124294255010252692, guid: 522efc9afd2e44ed4a1604a36658f0ba, type: 3}
    - {fileID: 6577736242046176163, guid: 522efc9afd2e44ed4a1604a36658f0ba, type: 3}
    - {fileID: 208435388225878645, guid: 522efc9afd2e44ed4a1604a36658f0ba, type: 3}
    - {fileID: -5810008528621116471, guid: 522efc9afd2e44ed4a1604a36658f0ba, type: 3}
    - {fileID: -9172249567589604163, guid: 522efc9afd2e44ed4a1604a36658f0ba, type: 3}
  m_AnimationClipSettings:
    serializedVersion: 2
    m_AdditiveReferencePoseClip: {fileID: 0}
    m_AdditiveReferencePoseTime: 0
    m_StartTime: 0
    m_StopTime: 0.5416667
    m_OrientationOffsetY: 0
    m_Level: 0
    m_CycleOffset: 0
    m_HasAdditiveReferencePose: 0
    m_LoopTime: 1
    m_LoopBlend: 0
    m_LoopBlendOrientation: 0
    m_LoopBlendPositionY: 0
    m_LoopBlendPositionXZ: 0
    m_KeepOriginalOrientation: 0
    m_KeepOriginalPositionY: 1
    m_KeepOriginalPositionXZ: 0
    m_HeightFromFeet: 0
    m_Mirror: 0
  m_EditorCurves: []
  m_EulerEditorCurves: []
  m_HasGenericRootTransform: 0
  m_HasMotionFloatCurves: 0
  m_Events: []
