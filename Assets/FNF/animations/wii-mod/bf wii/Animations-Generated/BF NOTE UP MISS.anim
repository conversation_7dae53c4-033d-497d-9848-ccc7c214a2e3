%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!74 &7400000
AnimationClip:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: BF NOTE UP MISS
  serializedVersion: 6
  m_Legacy: 0
  m_Compressed: 0
  m_UseHighQualityCurve: 1
  m_RotationCurves: []
  m_CompressedRotationCurves: []
  m_EulerCurves: []
  m_PositionCurves: []
  m_ScaleCurves: []
  m_FloatCurves: []
  m_PPtrCurves:
  - curve:
    - time: 0
      value: {fileID: 4524704011610011589, guid: 522efc9afd2e44ed4a1604a36658f0ba,
        type: 3}
    - time: 0.041666668
      value: {fileID: 4182069350966587033, guid: 522efc9afd2e44ed4a1604a36658f0ba,
        type: 3}
    - time: 0.083333336
      value: {fileID: -8104497947218866013, guid: 522efc9afd2e44ed4a1604a36658f0ba,
        type: 3}
    - time: 0.125
      value: {fileID: 7290442558526860955, guid: 522efc9afd2e44ed4a1604a36658f0ba,
        type: 3}
    - time: 0.16666667
      value: {fileID: 6941322479367181466, guid: 522efc9afd2e44ed4a1604a36658f0ba,
        type: 3}
    - time: 0.20833333
      value: {fileID: 5587512438190637126, guid: 522efc9afd2e44ed4a1604a36658f0ba,
        type: 3}
    - time: 0.25
      value: {fileID: 580111414938123564, guid: 522efc9afd2e44ed4a1604a36658f0ba,
        type: 3}
    - time: 0.29166666
      value: {fileID: 8279036473985852919, guid: 522efc9afd2e44ed4a1604a36658f0ba,
        type: 3}
    - time: 0.33333334
      value: {fileID: 3875295668309956635, guid: 522efc9afd2e44ed4a1604a36658f0ba,
        type: 3}
    - time: 0.375
      value: {fileID: -253975969746585515, guid: 522efc9afd2e44ed4a1604a36658f0ba,
        type: 3}
    - time: 0.41666666
      value: {fileID: 58205802409095188, guid: 522efc9afd2e44ed4a1604a36658f0ba, type: 3}
    - time: 0.45833334
      value: {fileID: 7649384093403883195, guid: 522efc9afd2e44ed4a1604a36658f0ba,
        type: 3}
    - time: 0.5
      value: {fileID: -5522183606852483255, guid: 522efc9afd2e44ed4a1604a36658f0ba,
        type: 3}
    attribute: m_Sprite
    path: sprite
    classID: 212
    script: {fileID: 0}
  m_SampleRate: 24
  m_WrapMode: 2
  m_Bounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  m_ClipBindingConstant:
    genericBindings:
    - serializedVersion: 2
      path: 891129758
      attribute: 0
      script: {fileID: 0}
      typeID: 212
      customType: 23
      isPPtrCurve: 1
    pptrCurveMapping:
    - {fileID: 4524704011610011589, guid: 522efc9afd2e44ed4a1604a36658f0ba, type: 3}
    - {fileID: 4182069350966587033, guid: 522efc9afd2e44ed4a1604a36658f0ba, type: 3}
    - {fileID: -8104497947218866013, guid: 522efc9afd2e44ed4a1604a36658f0ba, type: 3}
    - {fileID: 7290442558526860955, guid: 522efc9afd2e44ed4a1604a36658f0ba, type: 3}
    - {fileID: 6941322479367181466, guid: 522efc9afd2e44ed4a1604a36658f0ba, type: 3}
    - {fileID: 5587512438190637126, guid: 522efc9afd2e44ed4a1604a36658f0ba, type: 3}
    - {fileID: 580111414938123564, guid: 522efc9afd2e44ed4a1604a36658f0ba, type: 3}
    - {fileID: 8279036473985852919, guid: 522efc9afd2e44ed4a1604a36658f0ba, type: 3}
    - {fileID: 3875295668309956635, guid: 522efc9afd2e44ed4a1604a36658f0ba, type: 3}
    - {fileID: -253975969746585515, guid: 522efc9afd2e44ed4a1604a36658f0ba, type: 3}
    - {fileID: 58205802409095188, guid: 522efc9afd2e44ed4a1604a36658f0ba, type: 3}
    - {fileID: 7649384093403883195, guid: 522efc9afd2e44ed4a1604a36658f0ba, type: 3}
    - {fileID: -5522183606852483255, guid: 522efc9afd2e44ed4a1604a36658f0ba, type: 3}
  m_AnimationClipSettings:
    serializedVersion: 2
    m_AdditiveReferencePoseClip: {fileID: 0}
    m_AdditiveReferencePoseTime: 0
    m_StartTime: 0
    m_StopTime: 0.5416667
    m_OrientationOffsetY: 0
    m_Level: 0
    m_CycleOffset: 0
    m_HasAdditiveReferencePose: 0
    m_LoopTime: 1
    m_LoopBlend: 0
    m_LoopBlendOrientation: 0
    m_LoopBlendPositionY: 0
    m_LoopBlendPositionXZ: 0
    m_KeepOriginalOrientation: 0
    m_KeepOriginalPositionY: 1
    m_KeepOriginalPositionXZ: 0
    m_HeightFromFeet: 0
    m_Mirror: 0
  m_EditorCurves: []
  m_EulerEditorCurves: []
  m_HasGenericRootTransform: 0
  m_HasMotionFloatCurves: 0
  m_Events: []
