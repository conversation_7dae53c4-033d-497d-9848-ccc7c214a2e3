%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &4794707042560974175
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2888505400822889445}
  - component: {fileID: 2111035912777698222}
  - component: {fileID: 3326677268597946468}
  m_Layer: 0
  m_Name: spirit
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2888505400822889445
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4794707042560974175}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -3.68, y: 0.23, z: 0}
  m_LocalScale: {x: 1.9661, y: 1.9661, z: 1.9661}
  m_Children:
  - {fileID: 9045244197294121916}
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!95 &2111035912777698222
Animator:
  serializedVersion: 3
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4794707042560974175}
  m_Enabled: 1
  m_Avatar: {fileID: 0}
  m_Controller: {fileID: 9100000, guid: 6f2915961a5b3453bba4dbca61dad80b, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorControllerStateOnDisable: 0
--- !u!114 &3326677268597946468
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4794707042560974175}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3cd11897cbb984373adf5a343c63d792, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  animator: {fileID: 2111035912777698222}
  idleClip: {fileID: 7400000, guid: 22ea40a85e4d24870a25de6234c0f616, type: 2}
  singLongClip: []
  idleSpeedScale: 1
  icon: {fileID: 5725872871977202542, guid: e8837e1c4fe774b73a089450c1add6f2, type: 3}
  iconDie: {fileID: -1471702141919712319, guid: e8837e1c4fe774b73a089450c1add6f2,
    type: 3}
  iconFull: {fileID: -5659215995960606092, guid: e8837e1c4fe774b73a089450c1add6f2,
    type: 3}
  offsetShowCam: 1
  footTranform: {fileID: 0}
  focusCameraTranform: {fileID: 0}
  isBoyfriendPrefab: 0
  characterSelectionScale: 6.5
  color: {fileID: 0}
  colorChangeWhenMiss: {r: 0.33333334, g: 0.15686275, b: 0.75686276, a: 1}
  isSwapCharacter: 0
  tag: 
  tags: []
  isCustomHealthBarColor: 1
  healthBarColor: {r: 1, g: 0, b: 0.38823533, a: 1}
  SoundFontClip: {fileID: 0}
  SoundFontTextAsset: {fileID: 0}
  soundFontSampleRate: 48000
  SoundFontData:
    Items: []
    averageKey: 0
    offsetKey: 0
    soundSampleRate: 0
--- !u!1 &5975403864427287641
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 9045244197294121916}
  - component: {fileID: 4830443242915276589}
  m_Layer: 0
  m_Name: sprite
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &9045244197294121916
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5975403864427287641}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 2888505400822889445}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!212 &4830443242915276589
SpriteRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5975403864427287641}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10754, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_Sprite: {fileID: 6817623265196288906, guid: 5fb17de5b082c4e88801f6d6f0efcfec,
    type: 3}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_FlipX: 0
  m_FlipY: 0
  m_DrawMode: 0
  m_Size: {x: 0.47, y: 1.1}
  m_AdaptiveModeThreshold: 0.5
  m_SpriteTileMode: 0
  m_WasSpriteAssigned: 1
  m_MaskInteraction: 0
  m_SpriteSortPoint: 0
