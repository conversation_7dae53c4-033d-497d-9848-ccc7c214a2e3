fileFormatVersion: 2
guid: 466efd540881d40af9dc931e619100a6
TextureImporter:
  internalIDToNameTable:
  - first:
      213: -8054529555846676629
    second: fg tankhead 5 instance 10000
  - first:
      213: -6616978916504729714
    second: fg tankhead 5 instance 10001
  - first:
      213: 8568277658840184576
    second: fg tankhead 5 instance 10002
  - first:
      213: -3084520670495250708
    second: fg tankhead 5 instance 10003
  - first:
      213: 3066351534172789076
    second: fg tankhead 5 instance 10004
  - first:
      213: 5504519267271733806
    second: fg tankhead 5 instance 10005
  - first:
      213: 7605263783742427533
    second: fg tankhead 5 instance 10006
  - first:
      213: 4316696920889481712
    second: fg tankhead 5 instance 10007
  - first:
      213: -2837471572851397082
    second: fg tankhead 5 instance 10008
  - first:
      213: 4926969790369702367
    second: fg tankhead 5 instance 10009
  - first:
      213: -3748086187233108210
    second: fg tankhead 5 instance 10010
  - first:
      213: -1806784032867352981
    second: fg tankhead 5 instance 10011
  - first:
      213: 1060917729893712687
    second: fg tankhead 5 instance 10012
  - first:
      213: -707285209791935209
    second: fg tankhead 5 instance 10013
  - first:
      213: 8920643706463728200
    second: fg tankhead 5 instance 10014
  externalObjects: {}
  serializedVersion: 12
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMasterTextureLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  cookieLightType: 1
  platformSettings:
  - serializedVersion: 3
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 4096
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Android
    maxTextureSize: 4096
    resizeAlgorithm: 0
    textureFormat: 65
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 1
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: iPhone
    maxTextureSize: 4096
    resizeAlgorithm: 0
    textureFormat: 65
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 1
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Standalone
    maxTextureSize: 512
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 1
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Server
    maxTextureSize: 4096
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: WebGL
    maxTextureSize: 4096
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: fg tankhead 5 instance 10000
      rect:
        serializedVersion: 2
        x: 0
        y: 11
        width: 472
        height: 113
      alignment: 9
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b6fbfde2b00983090800000000000000
      internalID: -8054529555846676629
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: fg tankhead 5 instance 10001
      rect:
        serializedVersion: 2
        x: 0
        y: 11
        width: 472
        height: 113
      alignment: 9
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e878c73dbb4cb24a0800000000000000
      internalID: -6616978916504729714
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: fg tankhead 5 instance 10002
      rect:
        serializedVersion: 2
        x: 0
        y: 134
        width: 468
        height: 116
      alignment: 9
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 007b5105223a8e670800000000000000
      internalID: 8568277658840184576
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: fg tankhead 5 instance 10003
      rect:
        serializedVersion: 2
        x: 0
        y: 134
        width: 468
        height: 116
      alignment: 9
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ce6e7cceac49135d0800000000000000
      internalID: -3084520670495250708
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: fg tankhead 5 instance 10004
      rect:
        serializedVersion: 2
        x: 0
        y: 260
        width: 460
        height: 120
      alignment: 9
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 459d7de597edd8a20800000000000000
      internalID: 3066351534172789076
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: fg tankhead 5 instance 10005
      rect:
        serializedVersion: 2
        x: 0
        y: 260
        width: 460
        height: 120
      alignment: 9
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e2276010c1bf36c40800000000000000
      internalID: 5504519267271733806
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: fg tankhead 5 instance 10006
      rect:
        serializedVersion: 2
        x: 0
        y: 390
        width: 459
        height: 122
      alignment: 9
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d815c1ded135b8960800000000000000
      internalID: 7605263783742427533
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: fg tankhead 5 instance 10007
      rect:
        serializedVersion: 2
        x: 0
        y: 390
        width: 459
        height: 122
      alignment: 9
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0f9d3ac8afcf7eb30800000000000000
      internalID: 4316696920889481712
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: fg tankhead 5 instance 10008
      rect:
        serializedVersion: 2
        x: 0
        y: 390
        width: 459
        height: 122
      alignment: 9
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 62e6ce753a64f98d0800000000000000
      internalID: -2837471572851397082
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: fg tankhead 5 instance 10009
      rect:
        serializedVersion: 2
        x: 0
        y: 390
        width: 459
        height: 122
      alignment: 9
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: fd1211e8bec106440800000000000000
      internalID: 4926969790369702367
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: fg tankhead 5 instance 10010
      rect:
        serializedVersion: 2
        x: 0
        y: 390
        width: 459
        height: 122
      alignment: 9
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e0f1c6e887f1cfbc0800000000000000
      internalID: -3748086187233108210
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: fg tankhead 5 instance 10011
      rect:
        serializedVersion: 2
        x: 0
        y: 390
        width: 459
        height: 122
      alignment: 9
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b6ef71fae730de6e0800000000000000
      internalID: -1806784032867352981
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: fg tankhead 5 instance 10012
      rect:
        serializedVersion: 2
        x: 0
        y: 390
        width: 459
        height: 122
      alignment: 9
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f238a3aed0329be00800000000000000
      internalID: 1060917729893712687
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: fg tankhead 5 instance 10013
      rect:
        serializedVersion: 2
        x: 0
        y: 390
        width: 459
        height: 122
      alignment: 9
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 71dc67b59d73f26f0800000000000000
      internalID: -707285209791935209
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: fg tankhead 5 instance 10014
      rect:
        serializedVersion: 2
        x: 0
        y: 390
        width: 459
        height: 122
      alignment: 9
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 842389bff2e7ccb70800000000000000
      internalID: 8920643706463728200
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    physicsShape: []
    bones: []
    spriteID: 5e97eb03825dee720800000000000000
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    nameFileIdTable:
      fg tankhead 5 instance 10000: -8054529555846676629
      fg tankhead 5 instance 10001: -6616978916504729714
      fg tankhead 5 instance 10002: 8568277658840184576
      fg tankhead 5 instance 10003: -3084520670495250708
      fg tankhead 5 instance 10004: 3066351534172789076
      fg tankhead 5 instance 10005: 5504519267271733806
      fg tankhead 5 instance 10006: 7605263783742427533
      fg tankhead 5 instance 10007: 4316696920889481712
      fg tankhead 5 instance 10008: -2837471572851397082
      fg tankhead 5 instance 10009: 4926969790369702367
      fg tankhead 5 instance 10010: -3748086187233108210
      fg tankhead 5 instance 10011: -1806784032867352981
      fg tankhead 5 instance 10012: 1060917729893712687
      fg tankhead 5 instance 10013: -707285209791935209
      fg tankhead 5 instance 10014: 8920643706463728200
  spritePackingTag: 
  pSDRemoveMatte: 0
  pSDShowRemoveMatteOption: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
