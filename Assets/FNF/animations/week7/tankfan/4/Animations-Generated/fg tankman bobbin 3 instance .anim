%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!74 &7400000
AnimationClip:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 'fg tankman bobbin 3 instance '
  serializedVersion: 6
  m_Legacy: 0
  m_Compressed: 0
  m_UseHighQualityCurve: 1
  m_RotationCurves: []
  m_CompressedRotationCurves: []
  m_EulerCurves: []
  m_PositionCurves: []
  m_ScaleCurves: []
  m_FloatCurves: []
  m_PPtrCurves:
  - curve:
    - time: 0
      value: {fileID: 608669625690517812, guid: feb3c895dcad74636bd712a26bace77b,
        type: 3}
    - time: 0.041666668
      value: {fileID: 1119170364569276507, guid: feb3c895dcad74636bd712a26bace77b,
        type: 3}
    - time: 0.083333336
      value: {fileID: -1705300023631428305, guid: feb3c895dcad74636bd712a26bace77b,
        type: 3}
    - time: 0.125
      value: {fileID: 2791373898005194591, guid: feb3c895dcad74636bd712a26bace77b,
        type: 3}
    - time: 0.16666667
      value: {fileID: -8063207351177795190, guid: feb3c895dcad74636bd712a26bace77b,
        type: 3}
    - time: 0.20833333
      value: {fileID: 7015223278584569254, guid: feb3c895dcad74636bd712a26bace77b,
        type: 3}
    - time: 0.25
      value: {fileID: 6505211693774376597, guid: feb3c895dcad74636bd712a26bace77b,
        type: 3}
    - time: 0.29166666
      value: {fileID: -7619857344224882406, guid: feb3c895dcad74636bd712a26bace77b,
        type: 3}
    - time: 0.33333334
      value: {fileID: -7157787140448605233, guid: feb3c895dcad74636bd712a26bace77b,
        type: 3}
    - time: 0.375
      value: {fileID: 3752926491551832389, guid: feb3c895dcad74636bd712a26bace77b,
        type: 3}
    - time: 0.41666666
      value: {fileID: 255684807903870321, guid: feb3c895dcad74636bd712a26bace77b,
        type: 3}
    - time: 0.45833334
      value: {fileID: 4213617056038642392, guid: feb3c895dcad74636bd712a26bace77b,
        type: 3}
    - time: 0.5
      value: {fileID: 8615981188884821234, guid: feb3c895dcad74636bd712a26bace77b,
        type: 3}
    - time: 0.5416667
      value: {fileID: -2988060435056353099, guid: feb3c895dcad74636bd712a26bace77b,
        type: 3}
    attribute: m_Sprite
    path: sprite
    classID: 212
    script: {fileID: 0}
  m_SampleRate: 24
  m_WrapMode: 2
  m_Bounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  m_ClipBindingConstant:
    genericBindings:
    - serializedVersion: 2
      path: 891129758
      attribute: 0
      script: {fileID: 0}
      typeID: 212
      customType: 23
      isPPtrCurve: 1
    pptrCurveMapping:
    - {fileID: 608669625690517812, guid: feb3c895dcad74636bd712a26bace77b, type: 3}
    - {fileID: 1119170364569276507, guid: feb3c895dcad74636bd712a26bace77b, type: 3}
    - {fileID: -1705300023631428305, guid: feb3c895dcad74636bd712a26bace77b, type: 3}
    - {fileID: 2791373898005194591, guid: feb3c895dcad74636bd712a26bace77b, type: 3}
    - {fileID: -8063207351177795190, guid: feb3c895dcad74636bd712a26bace77b, type: 3}
    - {fileID: 7015223278584569254, guid: feb3c895dcad74636bd712a26bace77b, type: 3}
    - {fileID: 6505211693774376597, guid: feb3c895dcad74636bd712a26bace77b, type: 3}
    - {fileID: -7619857344224882406, guid: feb3c895dcad74636bd712a26bace77b, type: 3}
    - {fileID: -7157787140448605233, guid: feb3c895dcad74636bd712a26bace77b, type: 3}
    - {fileID: 3752926491551832389, guid: feb3c895dcad74636bd712a26bace77b, type: 3}
    - {fileID: 255684807903870321, guid: feb3c895dcad74636bd712a26bace77b, type: 3}
    - {fileID: 4213617056038642392, guid: feb3c895dcad74636bd712a26bace77b, type: 3}
    - {fileID: 8615981188884821234, guid: feb3c895dcad74636bd712a26bace77b, type: 3}
    - {fileID: -2988060435056353099, guid: feb3c895dcad74636bd712a26bace77b, type: 3}
  m_AnimationClipSettings:
    serializedVersion: 2
    m_AdditiveReferencePoseClip: {fileID: 0}
    m_AdditiveReferencePoseTime: 0
    m_StartTime: 0
    m_StopTime: 0.5833334
    m_OrientationOffsetY: 0
    m_Level: 0
    m_CycleOffset: 0
    m_HasAdditiveReferencePose: 0
    m_LoopTime: 1
    m_LoopBlend: 0
    m_LoopBlendOrientation: 0
    m_LoopBlendPositionY: 0
    m_LoopBlendPositionXZ: 0
    m_KeepOriginalOrientation: 0
    m_KeepOriginalPositionY: 1
    m_KeepOriginalPositionXZ: 0
    m_HeightFromFeet: 0
    m_Mirror: 0
  m_EditorCurves: []
  m_EulerEditorCurves: []
  m_HasGenericRootTransform: 0
  m_HasMotionFloatCurves: 0
  m_Events: []
