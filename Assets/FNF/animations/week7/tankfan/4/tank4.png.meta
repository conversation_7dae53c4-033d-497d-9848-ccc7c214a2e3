fileFormatVersion: 2
guid: feb3c895dcad74636bd712a26bace77b
TextureImporter:
  internalIDToNameTable:
  - first:
      213: 608669625690517812
    second: fg tankman bobbin 3 instance 10000
  - first:
      213: 1119170364569276507
    second: fg tankman bobbin 3 instance 10001
  - first:
      213: -1705300023631428305
    second: fg tankman bobbin 3 instance 10002
  - first:
      213: 2791373898005194591
    second: fg tankman bobbin 3 instance 10003
  - first:
      213: -8063207351177795190
    second: fg tankman bobbin 3 instance 10004
  - first:
      213: 7015223278584569254
    second: fg tankman bobbin 3 instance 10005
  - first:
      213: 6505211693774376597
    second: fg tankman bobbin 3 instance 10006
  - first:
      213: -7619857344224882406
    second: fg tankman bobbin 3 instance 10007
  - first:
      213: -7157787140448605233
    second: fg tankman bobbin 3 instance 10008
  - first:
      213: 3752926491551832389
    second: fg tankman bobbin 3 instance 10009
  - first:
      213: 255684807903870321
    second: fg tankman bobbin 3 instance 10010
  - first:
      213: 4213617056038642392
    second: fg tankman bobbin 3 instance 10011
  - first:
      213: 8615981188884821234
    second: fg tankman bobbin 3 instance 10012
  - first:
      213: -2988060435056353099
    second: fg tankman bobbin 3 instance 10013
  externalObjects: {}
  serializedVersion: 12
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMasterTextureLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  cookieLightType: 1
  platformSettings:
  - serializedVersion: 3
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 4096
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Android
    maxTextureSize: 4096
    resizeAlgorithm: 0
    textureFormat: 65
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 1
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: iPhone
    maxTextureSize: 4096
    resizeAlgorithm: 0
    textureFormat: 65
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 1
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Standalone
    maxTextureSize: 512
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 1
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Server
    maxTextureSize: 4096
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: WebGL
    maxTextureSize: 4096
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: fg tankman bobbin 3 instance 10000
      rect:
        serializedVersion: 2
        x: 0
        y: 705
        width: 399
        height: 319
      alignment: 9
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4317ad8bccd627800800000000000000
      internalID: 608669625690517812
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: fg tankman bobbin 3 instance 10001
      rect:
        serializedVersion: 2
        x: 0
        y: 705
        width: 399
        height: 319
      alignment: 9
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b50db475487188f00800000000000000
      internalID: 1119170364569276507
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: fg tankman bobbin 3 instance 10002
      rect:
        serializedVersion: 2
        x: 0
        y: 373
        width: 393
        height: 322
      alignment: 9
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f25cbffd9ae8558e0800000000000000
      internalID: -1705300023631428305
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: fg tankman bobbin 3 instance 10003
      rect:
        serializedVersion: 2
        x: 0
        y: 373
        width: 393
        height: 322
      alignment: 9
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f57a5e955c3fcb620800000000000000
      internalID: 2791373898005194591
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: fg tankman bobbin 3 instance 10004
      rect:
        serializedVersion: 2
        x: 403
        y: 375
        width: 391
        height: 320
      alignment: 9
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a89aab282abb91090800000000000000
      internalID: -8063207351177795190
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: fg tankman bobbin 3 instance 10005
      rect:
        serializedVersion: 2
        x: 403
        y: 375
        width: 391
        height: 320
      alignment: 9
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6ad951457641b5160800000000000000
      internalID: 7015223278584569254
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: fg tankman bobbin 3 instance 10006
      rect:
        serializedVersion: 2
        x: 403
        y: 45
        width: 390
        height: 320
      alignment: 9
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 59e8324c197274a50800000000000000
      internalID: 6505211693774376597
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: fg tankman bobbin 3 instance 10007
      rect:
        serializedVersion: 2
        x: 403
        y: 45
        width: 390
        height: 320
      alignment: 9
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a11135efd14d04690800000000000000
      internalID: -7619857344224882406
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: fg tankman bobbin 3 instance 10008
      rect:
        serializedVersion: 2
        x: 0
        y: 44
        width: 390
        height: 319
      alignment: 9
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: fcba3b3f48e6aac90800000000000000
      internalID: -7157787140448605233
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: fg tankman bobbin 3 instance 10009
      rect:
        serializedVersion: 2
        x: 0
        y: 44
        width: 390
        height: 319
      alignment: 9
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 549991d92c2151430800000000000000
      internalID: 3752926491551832389
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: fg tankman bobbin 3 instance 10010
      rect:
        serializedVersion: 2
        x: 0
        y: 44
        width: 390
        height: 319
      alignment: 9
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 17df2956aff5c8300800000000000000
      internalID: 255684807903870321
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: fg tankman bobbin 3 instance 10011
      rect:
        serializedVersion: 2
        x: 0
        y: 44
        width: 390
        height: 319
      alignment: 9
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8d2e5b44366c97a30800000000000000
      internalID: 4213617056038642392
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: fg tankman bobbin 3 instance 10012
      rect:
        serializedVersion: 2
        x: 0
        y: 44
        width: 390
        height: 319
      alignment: 9
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2f47deaed3d129770800000000000000
      internalID: 8615981188884821234
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: fg tankman bobbin 3 instance 10013
      rect:
        serializedVersion: 2
        x: 0
        y: 44
        width: 390
        height: 319
      alignment: 9
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5b02111add64886d0800000000000000
      internalID: -2988060435056353099
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    physicsShape: []
    bones: []
    spriteID: 5e97eb03825dee720800000000000000
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    nameFileIdTable:
      fg tankman bobbin 3 instance 10000: 608669625690517812
      fg tankman bobbin 3 instance 10001: 1119170364569276507
      fg tankman bobbin 3 instance 10002: -1705300023631428305
      fg tankman bobbin 3 instance 10003: 2791373898005194591
      fg tankman bobbin 3 instance 10004: -8063207351177795190
      fg tankman bobbin 3 instance 10005: 7015223278584569254
      fg tankman bobbin 3 instance 10006: 6505211693774376597
      fg tankman bobbin 3 instance 10007: -7619857344224882406
      fg tankman bobbin 3 instance 10008: -7157787140448605233
      fg tankman bobbin 3 instance 10009: 3752926491551832389
      fg tankman bobbin 3 instance 10010: 255684807903870321
      fg tankman bobbin 3 instance 10011: 4213617056038642392
      fg tankman bobbin 3 instance 10012: 8615981188884821234
      fg tankman bobbin 3 instance 10013: -2988060435056353099
  spritePackingTag: 
  pSDRemoveMatte: 0
  pSDShowRemoveMatteOption: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
