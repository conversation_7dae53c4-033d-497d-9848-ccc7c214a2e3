fileFormatVersion: 2
guid: a33016f2a5fb8491fa0f10a639cbc223
TextureImporter:
  internalIDToNameTable:
  - first:
      213: 3689410395574460883
    second: foreground man 3 instance 10000
  - first:
      213: 138057330280569597
    second: foreground man 3 instance 10001
  - first:
      213: -5242877862061852423
    second: foreground man 3 instance 10002
  - first:
      213: 3846081883012456994
    second: foreground man 3 instance 10003
  - first:
      213: 5409188869969150630
    second: foreground man 3 instance 10004
  - first:
      213: 8220162222218141061
    second: foreground man 3 instance 10005
  - first:
      213: -4920201197016902570
    second: foreground man 3 instance 10006
  - first:
      213: 78774985765361471
    second: foreground man 3 instance 10007
  - first:
      213: 8465016729622116117
    second: foreground man 3 instance 10008
  - first:
      213: 553403101165832342
    second: foreground man 3 instance 10009
  - first:
      213: -8215292706767156119
    second: foreground man 3 instance 10010
  - first:
      213: 2591580812097360354
    second: foreground man 3 instance 10011
  - first:
      213: 7638558270923102089
    second: foreground man 3 instance 10012
  - first:
      213: -1323363695357873434
    second: foreground man 3 instance 10013
  externalObjects: {}
  serializedVersion: 12
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMasterTextureLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  cookieLightType: 1
  platformSettings:
  - serializedVersion: 3
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 4096
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Android
    maxTextureSize: 4096
    resizeAlgorithm: 0
    textureFormat: 65
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 1
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: iPhone
    maxTextureSize: 4096
    resizeAlgorithm: 0
    textureFormat: 65
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 1
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Standalone
    maxTextureSize: 512
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 1
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Server
    maxTextureSize: 4096
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: WebGL
    maxTextureSize: 4096
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: foreground man 3 instance 10000
      rect:
        serializedVersion: 2
        x: 595
        y: 392
        width: 293
        height: 305
      alignment: 9
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3d169da153b633330800000000000000
      internalID: 3689410395574460883
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: foreground man 3 instance 10001
      rect:
        serializedVersion: 2
        x: 595
        y: 392
        width: 293
        height: 305
      alignment: 9
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: dfa1302196a7ae100800000000000000
      internalID: 138057330280569597
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: foreground man 3 instance 10002
      rect:
        serializedVersion: 2
        x: 0
        y: 383
        width: 288
        height: 310
      alignment: 9
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9f83b07cc5e8d37b0800000000000000
      internalID: -5242877862061852423
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: foreground man 3 instance 10003
      rect:
        serializedVersion: 2
        x: 0
        y: 383
        width: 288
        height: 310
      alignment: 9
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 222e50e5817006530800000000000000
      internalID: 3846081883012456994
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: foreground man 3 instance 10004
      rect:
        serializedVersion: 2
        x: 595
        y: 707
        width: 288
        height: 317
      alignment: 9
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6a2028d3e9c411b40800000000000000
      internalID: 5409188869969150630
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: foreground man 3 instance 10005
      rect:
        serializedVersion: 2
        x: 595
        y: 707
        width: 288
        height: 317
      alignment: 9
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 58dd5632df1e31270800000000000000
      internalID: 8220162222218141061
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: foreground man 3 instance 10006
      rect:
        serializedVersion: 2
        x: 0
        y: 703
        width: 288
        height: 321
      alignment: 9
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6541b54641fe7bbb0800000000000000
      internalID: -4920201197016902570
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: foreground man 3 instance 10007
      rect:
        serializedVersion: 2
        x: 0
        y: 703
        width: 288
        height: 321
      alignment: 9
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f371bb3ae6dd71100800000000000000
      internalID: 78774985765361471
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: foreground man 3 instance 10008
      rect:
        serializedVersion: 2
        x: 298
        y: 702
        width: 287
        height: 322
      alignment: 9
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5177dd2bdd7c97570800000000000000
      internalID: 8465016729622116117
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: foreground man 3 instance 10009
      rect:
        serializedVersion: 2
        x: 298
        y: 702
        width: 287
        height: 322
      alignment: 9
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 698c69e30351ea700800000000000000
      internalID: 553403101165832342
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: foreground man 3 instance 10010
      rect:
        serializedVersion: 2
        x: 298
        y: 702
        width: 287
        height: 322
      alignment: 9
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 96c49474fca6dfd80800000000000000
      internalID: -8215292706767156119
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: foreground man 3 instance 10011
      rect:
        serializedVersion: 2
        x: 298
        y: 702
        width: 287
        height: 322
      alignment: 9
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2e118cf840527f320800000000000000
      internalID: 2591580812097360354
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: foreground man 3 instance 10012
      rect:
        serializedVersion: 2
        x: 298
        y: 702
        width: 287
        height: 322
      alignment: 9
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9875e7ec64c910a60800000000000000
      internalID: 7638558270923102089
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: foreground man 3 instance 10013
      rect:
        serializedVersion: 2
        x: 298
        y: 702
        width: 287
        height: 322
      alignment: 9
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6e2bb850bb772ade0800000000000000
      internalID: -1323363695357873434
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    physicsShape: []
    bones: []
    spriteID: 5e97eb03825dee720800000000000000
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    nameFileIdTable:
      foreground man 3 instance 10000: 3689410395574460883
      foreground man 3 instance 10001: 138057330280569597
      foreground man 3 instance 10002: -5242877862061852423
      foreground man 3 instance 10003: 3846081883012456994
      foreground man 3 instance 10004: 5409188869969150630
      foreground man 3 instance 10005: 8220162222218141061
      foreground man 3 instance 10006: -4920201197016902570
      foreground man 3 instance 10007: 78774985765361471
      foreground man 3 instance 10008: 8465016729622116117
      foreground man 3 instance 10009: 553403101165832342
      foreground man 3 instance 10010: -8215292706767156119
      foreground man 3 instance 10011: 2591580812097360354
      foreground man 3 instance 10012: 7638558270923102089
      foreground man 3 instance 10013: -1323363695357873434
  spritePackingTag: 
  pSDRemoveMatte: 0
  pSDShowRemoveMatteOption: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
