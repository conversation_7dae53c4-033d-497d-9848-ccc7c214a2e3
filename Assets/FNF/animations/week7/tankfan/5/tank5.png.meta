fileFormatVersion: 2
guid: ba5863afa7d7647d7af07b34dbacdc8e
TextureImporter:
  internalIDToNameTable:
  - first:
      213: 5061156192948946608
    second: fg tankhead far right instance 10000
  - first:
      213: 5942392570723481838
    second: fg tankhead far right instance 10001
  - first:
      213: 2130439204086489073
    second: fg tankhead far right instance 10002
  - first:
      213: 8757738781963975476
    second: fg tankhead far right instance 10003
  - first:
      213: 6049791697839024685
    second: fg tankhead far right instance 10004
  - first:
      213: -5787755033712568527
    second: fg tankhead far right instance 10005
  - first:
      213: 5114801698143771705
    second: fg tankhead far right instance 10006
  - first:
      213: 8429742829339725437
    second: fg tankhead far right instance 10007
  - first:
      213: 5675112795820594140
    second: fg tankhead far right instance 10008
  - first:
      213: 651020924260288648
    second: fg tankhead far right instance 10009
  - first:
      213: 2723751396611709806
    second: fg tankhead far right instance 10010
  - first:
      213: -5997650834182649952
    second: fg tankhead far right instance 10011
  - first:
      213: -5330185586954959718
    second: fg tankhead far right instance 10012
  - first:
      213: 3471575470195841930
    second: fg tankhead far right instance 10013
  externalObjects: {}
  serializedVersion: 12
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMasterTextureLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  cookieLightType: 1
  platformSettings:
  - serializedVersion: 3
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 4096
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Android
    maxTextureSize: 4096
    resizeAlgorithm: 0
    textureFormat: 65
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 1
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: iPhone
    maxTextureSize: 4096
    resizeAlgorithm: 0
    textureFormat: 65
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 1
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Standalone
    maxTextureSize: 512
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 1
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Server
    maxTextureSize: 4096
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: WebGL
    maxTextureSize: 4096
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: fg tankhead far right instance 10000
      rect:
        serializedVersion: 2
        x: 633
        y: 130
        width: 312
        height: 437
      alignment: 9
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0b68d790eb6dc3640800000000000000
      internalID: 5061156192948946608
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: fg tankhead far right instance 10001
      rect:
        serializedVersion: 2
        x: 633
        y: 130
        width: 312
        height: 437
      alignment: 9
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ee8b4e51f8e977250800000000000000
      internalID: 5942392570723481838
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: fg tankhead far right instance 10002
      rect:
        serializedVersion: 2
        x: 0
        y: 124
        width: 307
        height: 440
      alignment: 9
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1ff14272b27d09d10800000000000000
      internalID: 2130439204086489073
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: fg tankhead far right instance 10003
      rect:
        serializedVersion: 2
        x: 0
        y: 124
        width: 307
        height: 440
      alignment: 9
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 43bff9e850db98970800000000000000
      internalID: 8757738781963975476
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: fg tankhead far right instance 10004
      rect:
        serializedVersion: 2
        x: 633
        y: 577
        width: 306
        height: 447
      alignment: 9
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d2a6dcafe7d25f350800000000000000
      internalID: 6049791697839024685
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: fg tankhead far right instance 10005
      rect:
        serializedVersion: 2
        x: 633
        y: 577
        width: 306
        height: 447
      alignment: 9
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 13755903673cdafa0800000000000000
      internalID: -5787755033712568527
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: fg tankhead far right instance 10006
      rect:
        serializedVersion: 2
        x: 0
        y: 574
        width: 307
        height: 450
      alignment: 9
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 93c9c6a8b0d6bf640800000000000000
      internalID: 5114801698143771705
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: fg tankhead far right instance 10007
      rect:
        serializedVersion: 2
        x: 0
        y: 574
        width: 307
        height: 450
      alignment: 9
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d7ec0bac0767cf470800000000000000
      internalID: 8429742829339725437
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: fg tankhead far right instance 10008
      rect:
        serializedVersion: 2
        x: 317
        y: 574
        width: 306
        height: 450
      alignment: 9
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: cd3192f050d02ce40800000000000000
      internalID: 5675112795820594140
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: fg tankhead far right instance 10009
      rect:
        serializedVersion: 2
        x: 317
        y: 574
        width: 306
        height: 450
      alignment: 9
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 88881e3d414e80900800000000000000
      internalID: 651020924260288648
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: fg tankhead far right instance 10010
      rect:
        serializedVersion: 2
        x: 317
        y: 574
        width: 306
        height: 450
      alignment: 9
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e6bd383d675bcc520800000000000000
      internalID: 2723751396611709806
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: fg tankhead far right instance 10011
      rect:
        serializedVersion: 2
        x: 317
        y: 574
        width: 306
        height: 450
      alignment: 9
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0a7c7bdd75014cca0800000000000000
      internalID: -5997650834182649952
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: fg tankhead far right instance 10012
      rect:
        serializedVersion: 2
        x: 317
        y: 574
        width: 306
        height: 450
      alignment: 9
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a9484aef2706706b0800000000000000
      internalID: -5330185586954959718
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: fg tankhead far right instance 10013
      rect:
        serializedVersion: 2
        x: 317
        y: 574
        width: 306
        height: 450
      alignment: 9
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a8b9bf73f738d2030800000000000000
      internalID: 3471575470195841930
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    physicsShape: []
    bones: []
    spriteID: 5e97eb03825dee720800000000000000
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    nameFileIdTable:
      fg tankhead far right instance 10000: 5061156192948946608
      fg tankhead far right instance 10001: 5942392570723481838
      fg tankhead far right instance 10002: 2130439204086489073
      fg tankhead far right instance 10003: 8757738781963975476
      fg tankhead far right instance 10004: 6049791697839024685
      fg tankhead far right instance 10005: -5787755033712568527
      fg tankhead far right instance 10006: 5114801698143771705
      fg tankhead far right instance 10007: 8429742829339725437
      fg tankhead far right instance 10008: 5675112795820594140
      fg tankhead far right instance 10009: 651020924260288648
      fg tankhead far right instance 10010: 2723751396611709806
      fg tankhead far right instance 10011: -5997650834182649952
      fg tankhead far right instance 10012: -5330185586954959718
      fg tankhead far right instance 10013: 3471575470195841930
  spritePackingTag: 
  pSDRemoveMatte: 0
  pSDShowRemoveMatteOption: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
