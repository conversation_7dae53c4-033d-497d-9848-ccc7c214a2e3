fileFormatVersion: 2
guid: 5d39848a3470d45bb94ef4c10a90d551
TextureImporter:
  internalIDToNameTable:
  - first:
      213: 6764644942020729366
    second: fg tankhead 4 instance 10000
  - first:
      213: 8524193615998407446
    second: fg tankhead 4 instance 10001
  - first:
      213: 6913222859775456787
    second: fg tankhead 4 instance 10002
  - first:
      213: -1563409867553585036
    second: fg tankhead 4 instance 10003
  - first:
      213: 4719071968397091660
    second: fg tankhead 4 instance 10004
  - first:
      213: 7396528229169577848
    second: fg tankhead 4 instance 10005
  - first:
      213: -144801345940250176
    second: fg tankhead 4 instance 10006
  - first:
      213: 1886322832428342817
    second: fg tankhead 4 instance 10007
  - first:
      213: -8814574159047741418
    second: fg tankhead 4 instance 10008
  - first:
      213: -9140173061872564405
    second: fg tankhead 4 instance 10009
  - first:
      213: 789911201928394309
    second: fg tankhead 4 instance 10010
  - first:
      213: 5828757081481972250
    second: fg tankhead 4 instance 10011
  - first:
      213: 2458382430186574430
    second: fg tankhead 4 instance 10012
  - first:
      213: 148363026803671263
    second: fg tankhead 4 instance 10013
  - first:
      213: 5576673431366042457
    second: fg tankhead 4 instance 10014
  externalObjects: {}
  serializedVersion: 12
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMasterTextureLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  cookieLightType: 1
  platformSettings:
  - serializedVersion: 3
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 4096
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Android
    maxTextureSize: 4096
    resizeAlgorithm: 0
    textureFormat: 65
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 1
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: iPhone
    maxTextureSize: 4096
    resizeAlgorithm: 0
    textureFormat: 65
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 1
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Standalone
    maxTextureSize: 512
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 1
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Server
    maxTextureSize: 4096
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: WebGL
    maxTextureSize: 4096
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: fg tankhead 4 instance 10000
      rect:
        serializedVersion: 2
        x: 0
        y: 478
        width: 590
        height: 124
      alignment: 9
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 61ad9c0fbb8d0ed50800000000000000
      internalID: 6764644942020729366
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: fg tankhead 4 instance 10001
      rect:
        serializedVersion: 2
        x: 0
        y: 478
        width: 590
        height: 124
      alignment: 9
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 613dc3e3ee40c4670800000000000000
      internalID: 8524193615998407446
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: fg tankhead 4 instance 10002
      rect:
        serializedVersion: 2
        x: 0
        y: 612
        width: 585
        height: 126
      alignment: 9
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 316b60a2093b0ff50800000000000000
      internalID: 6913222859775456787
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: fg tankhead 4 instance 10003
      rect:
        serializedVersion: 2
        x: 0
        y: 612
        width: 585
        height: 126
      alignment: 9
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 478c02e3207ad4ae0800000000000000
      internalID: -1563409867553585036
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: fg tankhead 4 instance 10004
      rect:
        serializedVersion: 2
        x: 0
        y: 748
        width: 580
        height: 131
      alignment: 9
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c43efc9d3f28d7140800000000000000
      internalID: 4719071968397091660
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: fg tankhead 4 instance 10005
      rect:
        serializedVersion: 2
        x: 0
        y: 748
        width: 580
        height: 131
      alignment: 9
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 87f3dc66c3fb5a660800000000000000
      internalID: 7396528229169577848
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: fg tankhead 4 instance 10006
      rect:
        serializedVersion: 2
        x: 0
        y: 889
        width: 574
        height: 135
      alignment: 9
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0c1ab3c61ff8dfdf0800000000000000
      internalID: -144801345940250176
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: fg tankhead 4 instance 10007
      rect:
        serializedVersion: 2
        x: 0
        y: 889
        width: 574
        height: 135
      alignment: 9
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 12e99963f909d2a10800000000000000
      internalID: 1886322832428342817
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: fg tankhead 4 instance 10008
      rect:
        serializedVersion: 2
        x: 0
        y: 889
        width: 574
        height: 135
      alignment: 9
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6103f9301875ca580800000000000000
      internalID: -8814574159047741418
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: fg tankhead 4 instance 10009
      rect:
        serializedVersion: 2
        x: 0
        y: 889
        width: 574
        height: 135
      alignment: 9
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b47cd75e605972180800000000000000
      internalID: -9140173061872564405
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: fg tankhead 4 instance 10010
      rect:
        serializedVersion: 2
        x: 0
        y: 889
        width: 574
        height: 135
      alignment: 9
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5463b039d0456fa00800000000000000
      internalID: 789911201928394309
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: fg tankhead 4 instance 10011
      rect:
        serializedVersion: 2
        x: 0
        y: 889
        width: 574
        height: 135
      alignment: 9
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a1a86cbfea7e3e050800000000000000
      internalID: 5828757081481972250
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: fg tankhead 4 instance 10012
      rect:
        serializedVersion: 2
        x: 0
        y: 889
        width: 574
        height: 135
      alignment: 9
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e5215519bcded1220800000000000000
      internalID: 2458382430186574430
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: fg tankhead 4 instance 10013
      rect:
        serializedVersion: 2
        x: 0
        y: 889
        width: 574
        height: 135
      alignment: 9
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: fd04f6603671f0200800000000000000
      internalID: 148363026803671263
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: fg tankhead 4 instance 10014
      rect:
        serializedVersion: 2
        x: 0
        y: 889
        width: 574
        height: 135
      alignment: 9
      pivot: {x: 0.5, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9538ee870f2546d40800000000000000
      internalID: 5576673431366042457
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    physicsShape: []
    bones: []
    spriteID: 5e97eb03825dee720800000000000000
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    nameFileIdTable:
      fg tankhead 4 instance 10000: 6764644942020729366
      fg tankhead 4 instance 10001: 8524193615998407446
      fg tankhead 4 instance 10002: 6913222859775456787
      fg tankhead 4 instance 10003: -1563409867553585036
      fg tankhead 4 instance 10004: 4719071968397091660
      fg tankhead 4 instance 10005: 7396528229169577848
      fg tankhead 4 instance 10006: -144801345940250176
      fg tankhead 4 instance 10007: 1886322832428342817
      fg tankhead 4 instance 10008: -8814574159047741418
      fg tankhead 4 instance 10009: -9140173061872564405
      fg tankhead 4 instance 10010: 789911201928394309
      fg tankhead 4 instance 10011: 5828757081481972250
      fg tankhead 4 instance 10012: 2458382430186574430
      fg tankhead 4 instance 10013: 148363026803671263
      fg tankhead 4 instance 10014: 5576673431366042457
  spritePackingTag: 
  pSDRemoveMatte: 0
  pSDShowRemoveMatteOption: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
