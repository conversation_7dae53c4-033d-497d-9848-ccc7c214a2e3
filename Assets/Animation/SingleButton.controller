%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!74 &7416372
AnimationClip:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: Normal
  serializedVersion: 6
  m_Legacy: 0
  m_Compressed: 0
  m_UseHighQualityCurve: 1
  m_RotationCurves: []
  m_CompressedRotationCurves: []
  m_EulerCurves: []
  m_PositionCurves: []
  m_ScaleCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: {x: 1, y: 1, z: 0}
        inSlope: {x: 0.049999952, y: -0.050000012, z: 1}
        outSlope: {x: 0.049999952, y: -0.050000012, z: 1}
        tangentMode: 0
      - time: 1
        value: {x: 1.05, y: 0.95, z: 1}
        inSlope: {x: 0, y: 0, z: 0.5}
        outSlope: {x: 0, y: 0, z: 0.5}
        tangentMode: 0
      - time: 2
        value: {x: 1, y: 1, z: 1}
        inSlope: {x: -0.049999952, y: 0.050000012, z: 0}
        outSlope: {x: -0.049999952, y: 0.050000012, z: 0}
        tangentMode: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    path: 
  m_FloatCurves: []
  m_PPtrCurves: []
  m_SampleRate: 10
  m_WrapMode: 0
  m_Bounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  m_ClipBindingConstant:
    genericBindings:
    - path: 0
      attribute: 3
      script: {fileID: 0}
      classID: 4
      customType: 0
      isPPtrCurve: 0
    pptrCurveMapping: []
  m_AnimationClipSettings:
    serializedVersion: 2
    m_AdditiveReferencePoseClip: {fileID: 0}
    m_AdditiveReferencePoseTime: 0
    m_StartTime: 0
    m_StopTime: 2
    m_OrientationOffsetY: 0
    m_Level: 0
    m_CycleOffset: 0
    m_HasAdditiveReferencePose: 0
    m_LoopTime: 1
    m_LoopBlend: 0
    m_LoopBlendOrientation: 0
    m_LoopBlendPositionY: 0
    m_LoopBlendPositionXZ: 0
    m_KeepOriginalOrientation: 0
    m_KeepOriginalPositionY: 1
    m_KeepOriginalPositionXZ: 0
    m_HeightFromFeet: 0
    m_Mirror: 0
  m_EditorCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 1
        inSlope: 0.049999952
        outSlope: 0.049999952
        tangentMode: 10
      - time: 1
        value: 1.05
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      - time: 2
        value: 1
        inSlope: -0.049999952
        outSlope: -0.049999952
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalScale.x
    path: 
    classID: 224
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 1
        inSlope: -0.050000012
        outSlope: -0.050000012
        tangentMode: 10
      - time: 1
        value: 0.95
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      - time: 2
        value: 1
        inSlope: 0.050000012
        outSlope: 0.050000012
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalScale.y
    path: 
    classID: 224
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 0
        inSlope: 1
        outSlope: 1
        tangentMode: 10
      - time: 1
        value: 1
        inSlope: 0.5
        outSlope: 0.5
        tangentMode: 10
      - time: 2
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalScale.z
    path: 
    classID: 224
    script: {fileID: 0}
  m_EulerEditorCurves: []
  m_HasGenericRootTransform: 0
  m_HasMotionFloatCurves: 0
  m_GenerateMotionCurves: 0
  m_Events: []
--- !u!74 &7444524
AnimationClip:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: Pressed
  serializedVersion: 6
  m_Legacy: 0
  m_Compressed: 0
  m_UseHighQualityCurve: 1
  m_RotationCurves: []
  m_CompressedRotationCurves: []
  m_EulerCurves: []
  m_PositionCurves: []
  m_ScaleCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: {x: 1, y: 1, z: 0}
        inSlope: {x: 0.8000002, y: 0.8000002, z: 0}
        outSlope: {x: 0.8000002, y: 0.8000002, z: 0}
        tangentMode: 0
      - time: 0.25
        value: {x: 1.2, y: 1.2, z: 0}
        inSlope: {x: 0.09999925, y: 0.09999925, z: 0}
        outSlope: {x: 0.09999925, y: 0.09999925, z: 0}
        tangentMode: 0
      - time: 0.3
        value: {x: 1.17, y: 1.17, z: 0}
        inSlope: {x: -0.06000012, y: -0.06000012, z: 0}
        outSlope: {x: -0.06000012, y: -0.06000012, z: 0}
        tangentMode: 0
      - time: 0.3625
        value: {x: 1.2, y: 1.2, z: 0}
        inSlope: {x: 0.14000079, y: 0.14000079, z: 0}
        outSlope: {x: 0.14000079, y: 0.14000079, z: 0}
        tangentMode: 0
      - time: 0.4125
        value: {x: 1.19, y: 1.19, z: 0}
        inSlope: {x: -0.19999988, y: -0.19999988, z: 0}
        outSlope: {x: -0.19999988, y: -0.19999988, z: 0}
        tangentMode: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    path: 
  m_FloatCurves: []
  m_PPtrCurves: []
  m_SampleRate: 80
  m_WrapMode: 0
  m_Bounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  m_ClipBindingConstant:
    genericBindings:
    - path: 0
      attribute: 3
      script: {fileID: 0}
      classID: 4
      customType: 0
      isPPtrCurve: 0
    pptrCurveMapping: []
  m_AnimationClipSettings:
    serializedVersion: 2
    m_AdditiveReferencePoseClip: {fileID: 0}
    m_AdditiveReferencePoseTime: 0
    m_StartTime: 0
    m_StopTime: 0.4125
    m_OrientationOffsetY: 0
    m_Level: 0
    m_CycleOffset: 0
    m_HasAdditiveReferencePose: 0
    m_LoopTime: 0
    m_LoopBlend: 0
    m_LoopBlendOrientation: 0
    m_LoopBlendPositionY: 0
    m_LoopBlendPositionXZ: 0
    m_KeepOriginalOrientation: 0
    m_KeepOriginalPositionY: 1
    m_KeepOriginalPositionXZ: 0
    m_HeightFromFeet: 0
    m_Mirror: 0
  m_EditorCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 1
        inSlope: 0.8000002
        outSlope: 0.8000002
        tangentMode: 10
      - time: 0.25
        value: 1.2
        inSlope: 0.09999925
        outSlope: 0.09999925
        tangentMode: 10
      - time: 0.3
        value: 1.17
        inSlope: -0.06000012
        outSlope: -0.06000012
        tangentMode: 10
      - time: 0.3625
        value: 1.2
        inSlope: 0.14000079
        outSlope: 0.14000079
        tangentMode: 10
      - time: 0.4125
        value: 1.19
        inSlope: -0.19999988
        outSlope: -0.19999988
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalScale.x
    path: 
    classID: 224
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 1
        inSlope: 0.8000002
        outSlope: 0.8000002
        tangentMode: 10
      - time: 0.25
        value: 1.2
        inSlope: 0.09999925
        outSlope: 0.09999925
        tangentMode: 10
      - time: 0.3
        value: 1.17
        inSlope: -0.06000012
        outSlope: -0.06000012
        tangentMode: 10
      - time: 0.3625
        value: 1.2
        inSlope: 0.14000079
        outSlope: 0.14000079
        tangentMode: 10
      - time: 0.4125
        value: 1.19
        inSlope: -0.19999988
        outSlope: -0.19999988
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalScale.y
    path: 
    classID: 224
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      - time: 0.25
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      - time: 0.3
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      - time: 0.3625
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      - time: 0.4125
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalScale.z
    path: 
    classID: 224
    script: {fileID: 0}
  m_EulerEditorCurves: []
  m_HasGenericRootTransform: 0
  m_HasMotionFloatCurves: 0
  m_GenerateMotionCurves: 0
  m_Events: []
--- !u!74 &7489074
AnimationClip:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: Disabled
  serializedVersion: 6
  m_Legacy: 0
  m_Compressed: 0
  m_UseHighQualityCurve: 1
  m_RotationCurves: []
  m_CompressedRotationCurves: []
  m_EulerCurves: []
  m_PositionCurves: []
  m_ScaleCurves: []
  m_FloatCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 1
        inSlope: -7.235294
        outSlope: -7.235294
        tangentMode: 10
      - time: 0.083333336
        value: 0.39705884
        inSlope: -7.235294
        outSlope: -7.235294
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_Color.r
    path: 
    classID: 114
    script: {fileID: -765806418, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 1
        inSlope: -7.235294
        outSlope: -7.235294
        tangentMode: 10
      - time: 0.083333336
        value: 0.39705884
        inSlope: -7.235294
        outSlope: -7.235294
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_Color.g
    path: 
    classID: 114
    script: {fileID: -765806418, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 1
        inSlope: -7.235294
        outSlope: -7.235294
        tangentMode: 10
      - time: 0.083333336
        value: 0.39705884
        inSlope: -7.235294
        outSlope: -7.235294
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_Color.b
    path: 
    classID: 114
    script: {fileID: -765806418, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_PPtrCurves: []
  m_SampleRate: 60
  m_WrapMode: 0
  m_Bounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  m_ClipBindingConstant:
    genericBindings:
    - path: 0
      attribute: 2526845255
      script: {fileID: -765806418, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
      classID: 114
      customType: 0
      isPPtrCurve: 0
    - path: 0
      attribute: 4215373228
      script: {fileID: -765806418, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
      classID: 114
      customType: 0
      isPPtrCurve: 0
    - path: 0
      attribute: 2334886179
      script: {fileID: -765806418, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
      classID: 114
      customType: 0
      isPPtrCurve: 0
    pptrCurveMapping: []
  m_AnimationClipSettings:
    serializedVersion: 2
    m_AdditiveReferencePoseClip: {fileID: 0}
    m_AdditiveReferencePoseTime: 0
    m_StartTime: 0
    m_StopTime: 0.083333336
    m_OrientationOffsetY: 0
    m_Level: 0
    m_CycleOffset: 0
    m_HasAdditiveReferencePose: 0
    m_LoopTime: 0
    m_LoopBlend: 0
    m_LoopBlendOrientation: 0
    m_LoopBlendPositionY: 0
    m_LoopBlendPositionXZ: 0
    m_KeepOriginalOrientation: 0
    m_KeepOriginalPositionY: 1
    m_KeepOriginalPositionXZ: 0
    m_HeightFromFeet: 0
    m_Mirror: 0
  m_EditorCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 1
        inSlope: -7.235294
        outSlope: -7.235294
        tangentMode: 10
      - time: 0.083333336
        value: 0.39705884
        inSlope: -7.235294
        outSlope: -7.235294
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_Color.r
    path: 
    classID: 114
    script: {fileID: -765806418, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 1
        inSlope: -7.235294
        outSlope: -7.235294
        tangentMode: 10
      - time: 0.083333336
        value: 0.39705884
        inSlope: -7.235294
        outSlope: -7.235294
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_Color.g
    path: 
    classID: 114
    script: {fileID: -765806418, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 1
        inSlope: -7.235294
        outSlope: -7.235294
        tangentMode: 10
      - time: 0.083333336
        value: 0.39705884
        inSlope: -7.235294
        outSlope: -7.235294
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_Color.b
    path: 
    classID: 114
    script: {fileID: -765806418, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_EulerEditorCurves: []
  m_HasGenericRootTransform: 0
  m_HasMotionFloatCurves: 0
  m_GenerateMotionCurves: 0
  m_Events: []
--- !u!74 &7499120
AnimationClip:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: Highlighted
  serializedVersion: 6
  m_Legacy: 0
  m_Compressed: 0
  m_UseHighQualityCurve: 1
  m_RotationCurves: []
  m_CompressedRotationCurves: []
  m_EulerCurves: []
  m_PositionCurves: []
  m_ScaleCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: {x: 1, y: 1, z: 0}
        inSlope: {x: 0.2999997, y: 0.2999997, z: 0}
        outSlope: {x: 0.2999997, y: 0.2999997, z: 0}
        tangentMode: 0
      - time: 0.16666667
        value: {x: 1.05, y: 1.05, z: 0}
        inSlope: {x: -0.3499997, y: -0.3499997, z: 0}
        outSlope: {x: -0.3499997, y: -0.3499997, z: 0}
        tangentMode: 0
      - time: 0.21666667
        value: {x: 1, y: 1, z: 0}
        inSlope: {x: -0.9999991, y: -0.9999991, z: 0}
        outSlope: {x: -0.9999991, y: -0.9999991, z: 0}
        tangentMode: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    path: 
  m_FloatCurves: []
  m_PPtrCurves: []
  m_SampleRate: 60
  m_WrapMode: 0
  m_Bounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  m_ClipBindingConstant:
    genericBindings:
    - path: 0
      attribute: 3
      script: {fileID: 0}
      classID: 4
      customType: 0
      isPPtrCurve: 0
    pptrCurveMapping: []
  m_AnimationClipSettings:
    serializedVersion: 2
    m_AdditiveReferencePoseClip: {fileID: 0}
    m_AdditiveReferencePoseTime: 0
    m_StartTime: 0
    m_StopTime: 0.21666667
    m_OrientationOffsetY: 0
    m_Level: 0
    m_CycleOffset: 0
    m_HasAdditiveReferencePose: 0
    m_LoopTime: 0
    m_LoopBlend: 0
    m_LoopBlendOrientation: 0
    m_LoopBlendPositionY: 0
    m_LoopBlendPositionXZ: 0
    m_KeepOriginalOrientation: 0
    m_KeepOriginalPositionY: 1
    m_KeepOriginalPositionXZ: 0
    m_HeightFromFeet: 0
    m_Mirror: 0
  m_EditorCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 1
        inSlope: 0.2999997
        outSlope: 0.2999997
        tangentMode: 10
      - time: 0.16666667
        value: 1.05
        inSlope: -0.3499997
        outSlope: -0.3499997
        tangentMode: 10
      - time: 0.21666667
        value: 1
        inSlope: -0.9999991
        outSlope: -0.9999991
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalScale.x
    path: 
    classID: 224
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 1
        inSlope: 0.2999997
        outSlope: 0.2999997
        tangentMode: 10
      - time: 0.16666667
        value: 1.05
        inSlope: -0.3499997
        outSlope: -0.3499997
        tangentMode: 10
      - time: 0.21666667
        value: 1
        inSlope: -0.9999991
        outSlope: -0.9999991
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalScale.y
    path: 
    classID: 224
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      - time: 0.16666667
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      - time: 0.21666667
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalScale.z
    path: 
    classID: 224
    script: {fileID: 0}
  m_EulerEditorCurves: []
  m_HasGenericRootTransform: 0
  m_HasMotionFloatCurves: 0
  m_GenerateMotionCurves: 0
  m_Events: []
--- !u!91 &9100000
AnimatorController:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: SingleButton
  serializedVersion: 5
  m_AnimatorParameters:
  - m_Name: Normal
    m_Type: 9
    m_DefaultFloat: 0
    m_DefaultInt: 0
    m_DefaultBool: 0
    m_Controller: {fileID: 9100000}
  - m_Name: Highlighted
    m_Type: 9
    m_DefaultFloat: 0
    m_DefaultInt: 0
    m_DefaultBool: 0
    m_Controller: {fileID: 9100000}
  - m_Name: Pressed
    m_Type: 9
    m_DefaultFloat: 0
    m_DefaultInt: 0
    m_DefaultBool: 0
    m_Controller: {fileID: 9100000}
  - m_Name: Disabled
    m_Type: 9
    m_DefaultFloat: 0
    m_DefaultInt: 0
    m_DefaultBool: 0
    m_Controller: {fileID: 9100000}
  m_AnimatorLayers:
  - serializedVersion: 5
    m_Name: Base Layer
    m_StateMachine: {fileID: 110795758}
    m_Mask: {fileID: 0}
    m_Motions: []
    m_Behaviours: []
    m_BlendingMode: 0
    m_SyncedLayerIndex: -1
    m_DefaultWeight: 0
    m_IKPass: 0
    m_SyncedLayerAffectsTiming: 0
    m_Controller: {fileID: 9100000}
--- !u!1101 &110129564
AnimatorStateTransition:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: 
  m_Conditions:
  - m_ConditionMode: 1
    m_ConditionEvent: Normal
    m_EventTreshold: 0
  m_DstStateMachine: {fileID: 0}
  m_DstState: {fileID: 110291490}
  m_Solo: 0
  m_Mute: 0
  m_IsExit: 0
  serializedVersion: 3
  m_TransitionDuration: 0.1
  m_TransitionOffset: 0
  m_ExitTime: 0.9
  m_HasExitTime: 0
  m_HasFixedDuration: 1
  m_InterruptionSource: 0
  m_OrderedInterruption: 1
  m_CanTransitionToSelf: 1
--- !u!1101 &110139576
AnimatorStateTransition:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: 
  m_Conditions:
  - m_ConditionMode: 1
    m_ConditionEvent: Disabled
    m_EventTreshold: 0
  m_DstStateMachine: {fileID: 0}
  m_DstState: {fileID: 110233140}
  m_Solo: 0
  m_Mute: 0
  m_IsExit: 0
  serializedVersion: 3
  m_TransitionDuration: 0.1
  m_TransitionOffset: 0
  m_ExitTime: 0.9
  m_HasExitTime: 0
  m_HasFixedDuration: 1
  m_InterruptionSource: 0
  m_OrderedInterruption: 1
  m_CanTransitionToSelf: 1
--- !u!1101 &110180638
AnimatorStateTransition:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: 
  m_Conditions:
  - m_ConditionMode: 1
    m_ConditionEvent: Pressed
    m_EventTreshold: 0
  m_DstStateMachine: {fileID: 0}
  m_DstState: {fileID: 110207194}
  m_Solo: 0
  m_Mute: 0
  m_IsExit: 0
  serializedVersion: 3
  m_TransitionDuration: 0.1
  m_TransitionOffset: 0
  m_ExitTime: 0.9
  m_HasExitTime: 0
  m_HasFixedDuration: 1
  m_InterruptionSource: 0
  m_OrderedInterruption: 1
  m_CanTransitionToSelf: 1
--- !u!1101 &110188792
AnimatorStateTransition:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: 
  m_Conditions: []
  m_DstStateMachine: {fileID: 0}
  m_DstState: {fileID: 110291490}
  m_Solo: 0
  m_Mute: 0
  m_IsExit: 0
  serializedVersion: 3
  m_TransitionDuration: 0.25
  m_TransitionOffset: 0
  m_ExitTime: 0
  m_HasExitTime: 1
  m_HasFixedDuration: 1
  m_InterruptionSource: 0
  m_OrderedInterruption: 1
  m_CanTransitionToSelf: 1
--- !u!1101 &110196010
AnimatorStateTransition:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: 
  m_Conditions:
  - m_ConditionMode: 1
    m_ConditionEvent: Highlighted
    m_EventTreshold: 0
  m_DstStateMachine: {fileID: 0}
  m_DstState: {fileID: 110250056}
  m_Solo: 0
  m_Mute: 0
  m_IsExit: 0
  serializedVersion: 3
  m_TransitionDuration: 0.1
  m_TransitionOffset: 0
  m_ExitTime: 0.9
  m_HasExitTime: 0
  m_HasFixedDuration: 1
  m_InterruptionSource: 0
  m_OrderedInterruption: 1
  m_CanTransitionToSelf: 1
--- !u!1102 &110207194
AnimatorState:
  serializedVersion: 5
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: Pressed
  m_Speed: 1
  m_CycleOffset: 0
  m_Transitions: []
  m_StateMachineBehaviours: []
  m_Position: {x: 50, y: 50, z: 0}
  m_IKOnFeet: 0
  m_WriteDefaultValues: 1
  m_Mirror: 0
  m_SpeedParameterActive: 0
  m_MirrorParameterActive: 0
  m_CycleOffsetParameterActive: 0
  m_Motion: {fileID: 7444524}
  m_Tag: 
  m_SpeedParameter: 
  m_MirrorParameter: 
  m_CycleOffsetParameter: 
--- !u!1102 &110233140
AnimatorState:
  serializedVersion: 5
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: Disabled
  m_Speed: 1
  m_CycleOffset: 0
  m_Transitions: []
  m_StateMachineBehaviours: []
  m_Position: {x: 50, y: 50, z: 0}
  m_IKOnFeet: 0
  m_WriteDefaultValues: 1
  m_Mirror: 0
  m_SpeedParameterActive: 0
  m_MirrorParameterActive: 0
  m_CycleOffsetParameterActive: 0
  m_Motion: {fileID: 7489074}
  m_Tag: 
  m_SpeedParameter: 
  m_MirrorParameter: 
  m_CycleOffsetParameter: 
--- !u!1102 &110250056
AnimatorState:
  serializedVersion: 5
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: Highlighted
  m_Speed: 1
  m_CycleOffset: 0
  m_Transitions:
  - {fileID: 110188792}
  m_StateMachineBehaviours: []
  m_Position: {x: 50, y: 50, z: 0}
  m_IKOnFeet: 0
  m_WriteDefaultValues: 1
  m_Mirror: 0
  m_SpeedParameterActive: 0
  m_MirrorParameterActive: 0
  m_CycleOffsetParameterActive: 0
  m_Motion: {fileID: 7499120}
  m_Tag: 
  m_SpeedParameter: 
  m_MirrorParameter: 
  m_CycleOffsetParameter: 
--- !u!1102 &110291490
AnimatorState:
  serializedVersion: 5
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: Normal
  m_Speed: 1
  m_CycleOffset: 0
  m_Transitions: []
  m_StateMachineBehaviours: []
  m_Position: {x: 50, y: 50, z: 0}
  m_IKOnFeet: 0
  m_WriteDefaultValues: 1
  m_Mirror: 0
  m_SpeedParameterActive: 0
  m_MirrorParameterActive: 0
  m_CycleOffsetParameterActive: 0
  m_Motion: {fileID: 7416372}
  m_Tag: 
  m_SpeedParameter: 
  m_MirrorParameter: 
  m_CycleOffsetParameter: 
--- !u!1107 &110795758
AnimatorStateMachine:
  serializedVersion: 5
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: Base Layer
  m_ChildStates:
  - serializedVersion: 1
    m_State: {fileID: 110291490}
    m_Position: {x: 60, y: 240, z: 0}
  - serializedVersion: 1
    m_State: {fileID: 110250056}
    m_Position: {x: 264, y: 0, z: 0}
  - serializedVersion: 1
    m_State: {fileID: 110207194}
    m_Position: {x: 336, y: 96, z: 0}
  - serializedVersion: 1
    m_State: {fileID: 110233140}
    m_Position: {x: 305, y: 195, z: 0}
  m_ChildStateMachines: []
  m_AnyStateTransitions:
  - {fileID: 110129564}
  - {fileID: 110196010}
  - {fileID: 110180638}
  - {fileID: 110139576}
  m_EntryTransitions: []
  m_StateMachineTransitions: {}
  m_StateMachineBehaviours: []
  m_AnyStatePosition: {x: 50, y: 20, z: 0}
  m_EntryPosition: {x: -72, y: 132, z: 0}
  m_ExitPosition: {x: 800, y: 120, z: 0}
  m_ParentStateMachinePosition: {x: 800, y: 20, z: 0}
  m_DefaultState: {fileID: 110291490}
