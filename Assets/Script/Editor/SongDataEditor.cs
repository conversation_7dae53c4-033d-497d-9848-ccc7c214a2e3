#define SpriteImporterData
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using System.Linq;
using System.IO;
using Unity.EditorCoroutine;
using System;
using Newtonsoft.Json;
using UnityEngine.Networking;
using System.Net;
using UnityEditor.CustomExtensions;
using Prankard.FlashSpriteSheetImporter;
using UnityEditor.U2D;

[CustomEditor(typeof(SongData))]
class SongDataEditor : Editor {
    int startWeek = 0;
    int numWeek = -1;

    int _lastStartWeek = -1;
    int _lastNumWeek = -1;
    string txtListWeekId = string.Empty;

    string gameVersion = string.Empty;
    string gamePackageId = string.Empty;
    bool isSetGameInfo = false;
    bool addNewSongToBottom = true;
    List<string> hardModes = new List<string>() { "-easy", "", "-hard", "-hell" };
    const int MAX_ASSET_BUNDLE = 50; // Assuming MAX_ASSET_BUNDLE is defined somewhere

    TargetStorages storage = TargetStorages.CloudflareR2;
    TargetStorages backupStorage = TargetStorages.GoogleCloud;
    string listFoodDir = "Assets/FNF/food-game/Data/FoodList.asset";

    GUIStyle errorStyle;
    string error = string.Empty;
    SongData importSongData;
    bool isNewSongOnly = false;
    bool isRebuildAssetBundle = true;
    bool isIncludeSongData = true;
    string modName = "";

    // Performance optimization fields - now using centralized cache

    // GUI optimization
    private bool _needsRepaint = true;
    private int _lastSongCount = -1;
    private string _lastErrorCheck = string.Empty;
    private double _lastErrorCheckTime = 0;
    private const double ERROR_CHECK_INTERVAL = 5.0; // seconds

    // Pagination
    private int _songsPerPage = 50;
    private int _currentPage = 0;
    private string _searchFilter = string.Empty;
    private List<Song> _filteredSongs = new List<Song>();
    private bool _showPagination = true;

    // Song editing
    private HashSet<int> _expandedSongs = new HashSet<int>();

    public override void OnInspectorGUI() {
        var songData = (SongData)target;

        // Performance: Only initialize once
        if (!isSetGameInfo)
        {
            isSetGameInfo = true;
            var buildConfig = Resources.Load<BuildConfig>("BuildConfig");
            gameVersion = buildConfig.GAME_VERSION;
            gamePackageId = buildConfig.APP_ID;
            if(songData.cloudSongDataGameVersion != "") gameVersion = songData.cloudSongDataGameVersion;
            if(songData.cloudSongDataGamePackageId != "") gamePackageId = songData.cloudSongDataGamePackageId;
            UpdateSongName(songData);
            _lastSongCount = songData.listSong.Count;
        }

        // Performance: Only recalculate when song count changes
        if(numWeek == -1 && songData.listSong.Count > 0)
        {
            numWeek = songData.listSong[songData.listSong.Count - 1].week - songData.listSong[0].week + 1;
        }

        // Performance: Check if we need to update filtered songs
        if (_lastSongCount != songData.listSong.Count || _needsRepaint)
        {
            UpdateFilteredSongs(songData);
            _lastSongCount = songData.listSong.Count;
            _needsRepaint = false;
        }

        // Performance: Clean cache periodically
        SongDataEditorCache.CleanupCache();

        if(error != "")
        {
            if (errorStyle == null)
            {
                errorStyle = new GUIStyle(EditorStyles.label);
                errorStyle.normal.textColor = Color.red;
                errorStyle.wordWrap = true;
            }
            GUILayout.Label("Song data error : " + error, errorStyle);
        }

        // Add search and pagination controls
        DrawSearchAndPaginationControls(songData);

        GUILayout.BeginHorizontal();
        if(GUILayout.Button("Update Index"))
        {
            EditorUtility.DisplayProgressBar("Updating Index", "Processing songs...", 0f);

            try
            {
                int week = -1;
                string lastWeekId = "....";
                bool lastIncludeInbuild = true;

                for (int i = 0; i < songData.listSong.Count; i++)
                {
                    var song = songData.listSong[i];
                    EditorUtility.DisplayProgressBar("Updating Index", $"Processing song {i + 1}/{songData.listSong.Count}", (float)i / songData.listSong.Count);

                    if(song.weekId != lastWeekId)
                    {
                        lastWeekId = song.weekId;
                        lastIncludeInbuild = song.isIncludeInBuild;
                        week++;
                    }
                    song.week = week;
                    song.isIncludeInBuild = lastIncludeInbuild;
                }
            }
            finally
            {
                EditorUtility.ClearProgressBar();
            }

            EditorUtility.SetDirty(songData);
            AssetDatabase.SaveAssets();

            CheckWeekRelationship(songData);
            UpdateSongName(songData);
            _needsRepaint = true;
        }
        GUILayout.EndHorizontal();

        GUILayout.BeginHorizontal();
        //Check error song data
        if(GUILayout.Button("Check Error"))
        {
            // Performance: Only check errors periodically or when forced
            var currentTime = EditorApplication.timeSinceStartup;
            if (currentTime - _lastErrorCheckTime > ERROR_CHECK_INTERVAL || string.IsNullOrEmpty(_lastErrorCheck))
            {
                _lastErrorCheckTime = currentTime;
                _lastErrorCheck = songData.GetSongDataError();

                //Check if a element of list string repeat
                var listWeekId = txtListWeekId.Split(",").Select(s => s.Trim()).ToList();
                //sort list week id by alphabet
                listWeekId.Sort();
                var lastWeekId = "";
                foreach (var weekId in listWeekId)
                {
                    if (weekId == lastWeekId)
                    {
                        _lastErrorCheck += ("\nSong data error week id bi lap lai: " + weekId);
                    }
                    lastWeekId = weekId;
                }
            }
            error = _lastErrorCheck;
        }
        if(GUILayout.Button("Check Error Server"))
        {
            error = songData.GetSongDataServerError();
        }
        if(GUILayout.Button("Check Url Exist"))
        {
            CheckSondataUrlExist(songData);
        }
        if(GUILayout.Button("Check LevelData Error"))
        {
            CheckLevelDataError();
        }

        GUILayout.EndHorizontal();

        GUILayout.BeginHorizontal();
        GUILayout.Label("Start week");
        startWeek = int.Parse(GUILayout.TextField(startWeek.ToString()));
        GUILayout.Label("Num week");
        numWeek = int.Parse(GUILayout.TextField(numWeek.ToString()));
        GUILayout.EndHorizontal();
        GUILayout.BeginHorizontal();
        //Show list week id from start week to start week + num week
        if(_lastNumWeek != numWeek || _lastStartWeek != startWeek)
        {
            _lastNumWeek = numWeek;
            _lastStartWeek = startWeek;
            List<string> listWeekId = new List<string>();

            for (int i = startWeek; i < startWeek + numWeek; i++)
            {
                var song = songData.listSong.Find(s => s.week == i);
                if(song != null) listWeekId.Add(song.weekId);
            }
            txtListWeekId = string.Join(", ", listWeekId);
        }
        txtListWeekId = GUILayout.TextArea(txtListWeekId);
        GUILayout.EndHorizontal();

        if(GUILayout.Button("Update song list follow the listWeekId"))
        {
            var listWeekId = txtListWeekId.Split(',').Select(s => s.Trim()).ToList();
            var listSong = new List<Song>();
            //Unique list week id
            listWeekId = listWeekId.Distinct().ToList();
            txtListWeekId = string.Join(", ", listWeekId);
            foreach (var weekId in listWeekId)
            {
                var songs = songData.listSong.FindAll(s => s.weekId == weekId);
                if(songs.Count > 0) listSong.AddRange(songs);
            }
            songData.listSong = listSong;

            EditorUtility.SetDirty(songData);
            AssetDatabase.SaveAssets();
            CheckWeekRelationship(songData);
            UpdateSongName(songData);
        }
        
        GUILayout.BeginHorizontal();
        //Add mod name to end of song singer
        modName = GUILayout.TextField(modName);
        if(GUILayout.Button("Them sau singer"))
        {
            foreach (var song in songData.listSong)
            {
                if (song.week < startWeek || song.week >= startWeek + numWeek) continue;
                song.singer = song.singer.Split(" - ")[0];
                song.singer += " - " + modName;
            }

            EditorUtility.SetDirty(songData);
            AssetDatabase.SaveAssets();
            UpdateSongName(songData);
        }
        if(GUILayout.Button("De len singer"))
        {
            foreach (var song in songData.listSong)
            {
                if (song.week < startWeek || song.week >= startWeek + numWeek) continue;
                song.singer = modName;
            }

            EditorUtility.SetDirty(songData);
            AssetDatabase.SaveAssets();
            UpdateSongName(songData);
        }
        GUILayout.EndHorizontal();

        if(GUILayout.Button("Update Icon"))
        {
            var listFood = AssetDatabase.LoadAssetAtPath<FoodList>(listFoodDir);
            var currentPageSongs = GetCurrentPageSongs();
            var songsToProcess = currentPageSongs.Where(song => song.week >= startWeek && song.week < startWeek + numWeek).ToList();

            EditorUtility.DisplayProgressBar("Updating Icons", "Processing songs...", 0f);

            try
            {
                for (int i = 0; i < songsToProcess.Count; i++)
                {
                    var song = songsToProcess[i];
                    EditorUtility.DisplayProgressBar("Updating Icons", $"Processing {song.id}...", (float)i / songsToProcess.Count);

                    var assetPath = SongDataEditorCache.GetMapAssetPath(song.id);
                    if (string.IsNullOrEmpty(assetPath))
                    {
                        Debug.LogError("Thieu map " + song.id);
                        continue;
                    }

                    var mapData = SongDataEditorCache.LoadAsset<GameObject>(assetPath)?.GetComponent<MapData>();
                    if(mapData != null)
                    {
                        if(mapData.myPlayer == null || mapData.opPlayer == null)
                        {
                            Debug.LogError("MapData bi loi, chua keo myPlayer hoac opPlayer " + song.id);
                            continue;
                        }
                        song.myCharacterIcon = mapData.myPlayer.icon;
                        song.opCharacterIcon = mapData.opPlayer.icon;

                        if(mapData.food == null)
                        {
                            using (var editingScope = new PrefabUtility.EditPrefabContentsScope(assetPath))
                            {
                                var prefabRoot = editingScope.prefabContentsRoot;
                                var mapData2 = prefabRoot.GetComponent<MapData>();
                                mapData2.food = FoodGameController.GetFood(listFood.list.ToList(), mapData2);
                            }
                        }
                    }
                }
            }
            finally
            {
                EditorUtility.ClearProgressBar();
            }

            EditorUtility.SetDirty(songData);
            AssetDatabase.SaveAssets();
            CheckWeekRelationship(songData);
        }
        
        if(GUILayout.Button("Update MapData : (MusicData) & hasVocal & Mukbang & Voice Data"))
        {
            var currentPageSongs = GetCurrentPageSongs();
            var songsToProcess = currentPageSongs.Where(song => song.week >= startWeek && song.week < startWeek + numWeek).ToList();

            EditorUtility.DisplayProgressBar("Updating MapData", "Processing songs...", 0f);

            try
            {
                for (int i = 0; i < songsToProcess.Count; i++)
                {
                    var song = songsToProcess[i];
                    EditorUtility.DisplayProgressBar("Updating MapData", $"Processing {song.id}...", (float)i / songsToProcess.Count);

                    var assetPath = SongDataEditorCache.GetMapAssetPath(song.id);
                    if (string.IsNullOrEmpty(assetPath))
                    {
                        Debug.LogError("Thieu map " + song.id);
                        continue;
                    }

                    var musicPaths = SongDataEditorCache.GetMusicAssetPaths(song.id);
                    var assetPathInst = musicPaths[0];
                    var assetPathVoices = musicPaths[1];
                    var assetPathDataVoices = musicPaths[2];

                    if (string.IsNullOrEmpty(assetPathInst))
                    {
                        Debug.LogWarning("Thieu bai hat Inst " + song.id);
                        continue;
                    }

                    if (string.IsNullOrEmpty(assetPathVoices))
                    {
                        song.hasVocal = false;
                        Debug.LogWarning("Thieu bai hat Voices " + song.id);
                    }

                    try
                    {
                        using (var editingScope = new PrefabUtility.EditPrefabContentsScope(assetPath))
                        {
                            var prefabRoot = editingScope.prefabContentsRoot;
                            var mapData = prefabRoot.GetComponent<MapData>();

                            if(!string.IsNullOrEmpty(assetPathInst))
                                mapData.musicInst = SongDataEditorCache.LoadAsset<AudioClip>(assetPathInst);
                            if(!string.IsNullOrEmpty(assetPathVoices))
                                mapData.musicVoice = SongDataEditorCache.LoadAsset<AudioClip>(assetPathVoices);
                            if(!string.IsNullOrEmpty(assetPathDataVoices))
                                mapData.voiceData = SongDataEditorCache.LoadAsset<TextAsset>(assetPathDataVoices);
                        }
                    }
                    catch(Exception e)
                    {
                        Debug.LogError("Error edit song " + song.id + ": " + e.Message);
                    }
                }
            }
            finally
            {
                EditorUtility.ClearProgressBar();
            }

            EditorUtility.SetDirty(songData);
            AssetDatabase.SaveAssets();
            CheckWeekRelationship(songData);
        }

        if(GUILayout.Button("Update asset bundle"))
        {
            foreach(var hardMode in hardModes)
            {
                foreach (var song in songData.listSong)
                {
                    if (song.isIncludeInBuild) continue;
                    var assetPath = "Assets/FNF/Resources/maps/" + song.id + hardMode + ".prefab";
                    if (System.IO.File.Exists(assetPath))
                    {
                        AssetImporter.GetAtPath(assetPath).SetAssetBundleNameAndVariant(song.weekId, "");
                    }
                    else
                    {
                        assetPath = "Assets/FNF/maps/" + song.id + hardMode + ".prefab";
                        if (System.IO.File.Exists(assetPath))
                        {
                            AssetImporter.GetAtPath(assetPath).SetAssetBundleNameAndVariant(song.weekId, "");
                        }
                        else if(hardMode == "")
                        {
                            Debug.LogError("Thieu map de cho vao bundle " + song.id);
                        }
                    }
                }
            }
        }

        GUILayout.BeginHorizontal();
        if(GUILayout.Button("Set Include In Build"))
        {
            EditorUtility.DisplayProgressBar("Setting Include In Build", "Processing songs...", 0f);

            try
            {
                for (int i = 0; i < songData.listSong.Count; i++)
                {
                    var song = songData.listSong[i];
                    EditorUtility.DisplayProgressBar("Setting Include In Build", $"Processing song {i + 1}/{songData.listSong.Count}", (float)i / songData.listSong.Count);
                    song.isIncludeInBuild = song.week < numWeek;
                }
            }
            finally
            {
                EditorUtility.ClearProgressBar();
            }

            // Reset error check cache to force refresh
            _lastErrorCheck = string.Empty;
            _lastErrorCheckTime = 0;
            error = songData.GetSongDataError();

            EditorUtility.SetDirty(songData);
            AssetDatabase.SaveAssets();
            UpdateSongName(songData);
            _needsRepaint = true;
        }
        if(GUILayout.Button("Move Map Folder"))
        {
            MoveMapFolder(songData);
        }
        GUILayout.EndHorizontal();
        GUILayout.BeginHorizontal();
        if (GUILayout.Button("Update play asset delivery"))
        {
            UpdatePlayAssetDelivery(songData);
        }
        GUILayout.EndHorizontal();
        GUILayout.BeginHorizontal();
        if(GUILayout.Button("Set Fast Follow"))
        {
            foreach (var song in songData.listSong)
            {
                song.isFastFollow = !song.isIncludeInBuild && song.week <= numWeek;
                if(song.isFastFollow) song.isInstallTime = false;
            }

            EditorUtility.SetDirty(songData);
            AssetDatabase.SaveAssets();
            error = songData.GetSongDataError();
        }
        if(GUILayout.Button("Set Install Time"))
        {
            foreach (var song in songData.listSong)
            {
                song.isInstallTime = !song.isIncludeInBuild && song.week <= numWeek;
                if(song.isInstallTime) song.isFastFollow = false;
            }

            EditorUtility.SetDirty(songData);
            AssetDatabase.SaveAssets();
            error = songData.GetSongDataError();
        }
        GUILayout.EndHorizontal();

        GUILayout.BeginHorizontal();
        if(GUILayout.Button("Optimize icon"))
        {
            List<Sprite> listSprite = new List<Sprite>();
            foreach (var song in songData.listSong)
            {
                if (song.week < startWeek || song.week >= startWeek + numWeek) continue;
                var bfIconPath = AssetDatabase.GetAssetPath(song.myCharacterIcon);
                var opIconPath = AssetDatabase.GetAssetPath(song.opCharacterIcon);
                if(song.myCharacterIcon != null && bfIconPath != "" && bfIconPath.IndexOf("FNF/Icons") == -1)
                {
                    Debug.Log(bfIconPath);
                    var bfIconAssetPath = bfIconPath.Substring(bfIconPath.IndexOf("Assets/") + "Assets/".Length);
                    var iconName = bfIconAssetPath.Substring(bfIconAssetPath.LastIndexOf("FNF/") + 1).Replace(".png", "").Replace("/", "_");
                    var bfIconAssetPath2 = "Assets/FNF/Icons/" + iconName + "_bf_skip.png";
                    if(!System.IO.File.Exists(bfIconAssetPath2))
                    {
                        var success = song.myCharacterIcon.MakeIcon(bfIconAssetPath2);
                        if(success)
                        {
                            song.myCharacterIcon = AssetDatabase.LoadAssetAtPath<Sprite>(bfIconAssetPath2);
                        }
                    }
                    else
                    {
                        song.myCharacterIcon = AssetDatabase.LoadAssetAtPath<Sprite>(bfIconAssetPath2);
                    }
                }

                if(song.opCharacterIcon != null && opIconPath != "" && opIconPath.IndexOf("FNF/Icons") == -1)
                {
                    var opIconAssetPath = opIconPath.Substring(opIconPath.IndexOf("Assets/") + "Assets/".Length);
                    var iconName = opIconAssetPath.Substring(opIconAssetPath.LastIndexOf("FNF/") + 1).Replace(".png", "").Replace("/", "_");
                    var opIconAssetPath2 = "Assets/FNF/Icons/" + iconName + "_op_skip.png";
                    if(!System.IO.File.Exists(opIconAssetPath2))
                    {
                        var success = song.opCharacterIcon.MakeIcon(opIconAssetPath2);
                        if(success)
                        {
                            song.opCharacterIcon = AssetDatabase.LoadAssetAtPath<Sprite>(opIconAssetPath2);
                        }
                    }
                    else
                    {
                        song.opCharacterIcon = AssetDatabase.LoadAssetAtPath<Sprite>(opIconAssetPath2);
                    }
                }
                if(listSprite.Contains(song.myCharacterIcon) == false) listSprite.Add(song.myCharacterIcon);
                if(listSprite.Contains(song.opCharacterIcon) == false) listSprite.Add(song.opCharacterIcon);
            }
            //Create atlas from listSprite
            var pathNoExtension = "Assets/FNF/Icons/icon_atlas";
            SpriteAtlasAsset atlas = new SpriteAtlasAsset();
            AnimationCreator.SetAtlasSetting(atlas, 4096, pathNoExtension);
            atlas.Add(listSprite.ToArray());
            SpriteAtlasAsset.Save(atlas, pathNoExtension + ".spriteatlasv2");
            
            EditorUtility.SetDirty(songData);
            AssetDatabase.SaveAssets();
        }

        GUILayout.EndHorizontal();

        DrawLine();
        GUILayout.Label("Dùng gameversion và packageId từ build config khi upload");
        GUILayout.BeginHorizontal();
        if (GUILayout.Button("Make Cloud Uploader"))
        {
            if (this.storage == TargetStorages.None)
            {
                throw new Exception("Chưa chọn storage");
            }
            
            var currentSpritePackerMode = EditorSettings.spritePackerMode;
            try
            {
                #if SpriteImporterData
                SpriteImporterData.FixImagesImportByReSize();
                #endif
                Debug.LogError(songData.GetSongDataServerError());
                Debug.LogError(songData.GetSongDataError());
                EditorSettings.spritePackerMode = SpritePackerMode.SpriteAtlasV2;
                MakeCloudUploader(songData);
            }
            finally
            {
                EditorSettings.spritePackerMode = currentSpritePackerMode;
            }
        }
        storage = (TargetStorages)EditorGUILayout.EnumPopup("Chọn storage sẽ upload: ", storage);

        GUILayout.EndHorizontal();
        backupStorage = (TargetStorages)EditorGUILayout.EnumPopup("Chọn backup server (None - không backup): ", backupStorage);
        GUILayout.BeginHorizontal();

        GUILayout.EndHorizontal();

        GUILayout.BeginHorizontal();
        isNewSongOnly = GUILayout.Toggle(isNewSongOnly, "Chỉ upload những bài hát mới");
        isRebuildAssetBundle = GUILayout.Toggle(isRebuildAssetBundle, "Rebuild asset bundle");
        isIncludeSongData = GUILayout.Toggle(isIncludeSongData, "Include song data");
        GUILayout.EndHorizontal();
        GUILayout.Label("Command upload to server:");
        GUILayout.BeginHorizontal();
        //A text area contain command to upload to server
        GUILayout.TextArea($"cd {Application.dataPath};cd ..;cd ..;cd Funkin-master/cloud-uploader;npm run uploadMap {Application.dataPath}/Resources/cloudUploader.json r2");
        GUILayout.EndHorizontal();
        GUILayout.Label("Command upload to server:");
        GUILayout.BeginHorizontal();
        //A text area contain command to upload to server
        GUILayout.TextArea($"cd {Application.dataPath};cd ..;cd ..;cd ..;cd Funkin-master/cloud-uploader;npm run uploadMap {Application.dataPath}/Resources/cloudUploader.json r2");
        GUILayout.EndHorizontal();
        DrawLine();
        GUILayout.BeginHorizontal();
        if (GUILayout.Button("Sync with cloud SongData", GUILayout.ExpandWidth(true)))
        {
            EditorCoroutine.Start(SyncWithCloudSong());
        }

        gameVersion = GUILayout.TextField(gameVersion);
        
        gamePackageId = GUILayout.TextField(gamePackageId);

        GUILayout.EndHorizontal();

        addNewSongToBottom = EditorGUILayout.Toggle("Thêm bài hát mới vào cuối", addNewSongToBottom);
        DrawLine();
        GUILayout.BeginHorizontal();
        if(GUILayout.Button("Move dialog text out of resource folder"))
        {
            foreach (var song in songData.listSong)
            {
                if (song.week < startWeek || song.week >= startWeek + numWeek) continue;
                var assetPath = "Assets/FNF/Resources/maps/" + song.id + ".prefab";
                if (System.IO.File.Exists(assetPath))
                {
                }
                else
                {
                    assetPath = "";
                    var assetPath2 = "Assets/FNF/maps/" + song.id + ".prefab";
                    if (System.IO.File.Exists(assetPath2))
                    {
                        assetPath = assetPath2;
                    }
                    else
                    {
                        Debug.LogError("Thieu map " + song.id);
                    }
                }


                if (assetPath != "") MoveDialogTextOutOfResourceFolder(assetPath, song.id);
            }

            EditorUtility.SetDirty(songData);
            AssetDatabase.SaveAssets();
        }
        GUILayout.EndHorizontal();

        GUILayout.BeginHorizontal();
        //Import song data song list to current song data
        importSongData = (SongData)EditorGUILayout.ObjectField("Import SongData", importSongData, typeof(SongData), false);
        if(GUILayout.Button("Import SongData"))
        {
            if(importSongData != null)
            {
                foreach (var song in importSongData.listSong)
                {
                    var findSong = songData.listSong.Find(s => s.id == song.id && s.weekId == song.weekId);
                    if(findSong != null)
                    {
                        Debug.LogWarning("Da co bai hat " + song.id + " week id " + song.weekId + " find song : " + (findSong != null ? findSong.weekId : ""));
                    }
                    else
                    {
                        songData.listSong.Add(song);
                    }
                }
                foreach (var song in songData.listSong)
                {
                    if (song.myCharacterIcon == null)
                    {
                        song.myCharacterIcon = AssetDatabase.LoadAssetAtPath<Sprite>("Assets/FNF/Icons/NF_images_iconGrid_bf_skip.png");
                    }
                    if (song.myCharacterIcon == null && song.bfIconAssetUrl != "")
                    {
                        var assetPath = "Assets/FNF/Icons/" + song.bfIconAssetUrl.Substring(song.bfIconAssetUrl.LastIndexOf("FNF/") + 1).Replace(".png", "").Replace("/", "_") + "_bf.png";
                        song.myCharacterIcon = AssetDatabase.LoadAssetAtPath<Sprite>(assetPath);
                    }
                    if (song.opCharacterIcon == null && song.opIconAssetUrl != "")
                    {
                        var assetPath = "Assets/FNF/Icons/" + song.opIconAssetUrl.Substring(song.opIconAssetUrl.LastIndexOf("FNF/") + 1).Replace(".png", "").Replace("/", "_") + "_op.png";
                        song.opCharacterIcon = AssetDatabase.LoadAssetAtPath<Sprite>(assetPath);
                    }
                }
                EditorUtility.SetDirty(songData);
                AssetDatabase.SaveAssets();
                error = songData.GetSongDataError();
            }
        }
        GUILayout.EndHorizontal();

        // Performance debug section
        DrawLine();
        EditorGUILayout.BeginVertical("box");
        GUILayout.Label("Performance & Debug", EditorStyles.boldLabel);
        DrawDebugInfo();
        EditorGUILayout.EndVertical();

        // Custom song list display with pagination
        DrawLine();
        DrawSongListWithPagination(songData);

        // Draw other properties except listSong
        DrawPropertiesExcluding(serializedObject, "listSong");
        serializedObject.ApplyModifiedProperties();
    }

    void SaveSongData()
    {
        var songData = (SongData)target;
        EditorUtility.SetDirty(songData);
        AssetDatabase.SaveAssets();
    }

    // Performance optimization methods
    private void DrawSearchAndPaginationControls(SongData songData)
        {
            EditorGUILayout.BeginVertical("box");

            // Search and filter controls
            GUILayout.BeginHorizontal();
            GUILayout.Label("Search:", GUILayout.Width(50));
            var newSearchFilter = GUILayout.TextField(_searchFilter, GUILayout.ExpandWidth(true));
            if (newSearchFilter != _searchFilter)
            {
                _searchFilter = newSearchFilter;
                UpdateFilteredSongs(songData);
            }

            if (GUILayout.Button("Clear", GUILayout.Width(50)))
            {
                _searchFilter = string.Empty;
                UpdateFilteredSongs(songData);
            }
            GUILayout.EndHorizontal();

            // Pagination controls
            GUILayout.BeginHorizontal();
            _showPagination = GUILayout.Toggle(_showPagination, "Enable Pagination", GUILayout.Width(120));
            if (_showPagination)
            {
                GUILayout.Label("Songs per page:", GUILayout.Width(100));
                var newSongsPerPage = EditorGUILayout.IntField(_songsPerPage, GUILayout.Width(50));
                if (newSongsPerPage != _songsPerPage && newSongsPerPage > 0)
                {
                    _songsPerPage = newSongsPerPage;
                    _currentPage = 0; // Reset to first page
                }
            }
            GUILayout.EndHorizontal();

            // Page navigation
            if (_showPagination && _filteredSongs.Count > _songsPerPage)
            {
                var totalPages = Mathf.CeilToInt((float)_filteredSongs.Count / _songsPerPage);
                var startIndex = _currentPage * _songsPerPage;
                var endIndex = Mathf.Min(startIndex + _songsPerPage, _filteredSongs.Count);

                GUILayout.BeginHorizontal();
                GUILayout.Label($"Showing {startIndex + 1}-{endIndex} of {_filteredSongs.Count} songs (Page {_currentPage + 1}/{totalPages})");
                GUILayout.FlexibleSpace();

                GUI.enabled = _currentPage > 0;
                if (GUILayout.Button("<<", GUILayout.Width(30))) _currentPage = 0;
                if (GUILayout.Button("<", GUILayout.Width(30))) _currentPage = Mathf.Max(0, _currentPage - 1);
                GUI.enabled = _currentPage < totalPages - 1;
                if (GUILayout.Button(">", GUILayout.Width(30))) _currentPage = Mathf.Min(totalPages - 1, _currentPage + 1);
                if (GUILayout.Button(">>", GUILayout.Width(30))) _currentPage = totalPages - 1;
                GUI.enabled = true;

                GUILayout.EndHorizontal();

                // Quick page jump
                GUILayout.BeginHorizontal();
                GUILayout.Label("Go to page:", GUILayout.Width(80));
                var pageInput = EditorGUILayout.IntField(_currentPage + 1, GUILayout.Width(50));
                if (pageInput != _currentPage + 1)
                {
                    _currentPage = Mathf.Clamp(pageInput - 1, 0, totalPages - 1);
                }
                GUILayout.Label($"/ {totalPages}");
                GUILayout.EndHorizontal();
            }
            else if (!_showPagination)
            {
                GUILayout.Label($"Total songs: {_filteredSongs.Count}");
            }

            EditorGUILayout.EndVertical();
        }

        private void UpdateFilteredSongs(SongData songData)
        {
            if (string.IsNullOrEmpty(_searchFilter))
            {
                _filteredSongs = songData.listSong.ToList();
            }
            else
            {
                var filter = _searchFilter.ToLower();
                _filteredSongs = songData.listSong.Where(s =>
                    s.id.ToLower().Contains(filter) ||
                    s.songName.ToLower().Contains(filter) ||
                    s.singer.ToLower().Contains(filter) ||
                    s.weekId.ToLower().Contains(filter)
                ).ToList();
            }

            // Reset page if out of bounds
            if (_showPagination)
            {
                var totalPages = Mathf.CeilToInt((float)_filteredSongs.Count / _songsPerPage);
                _currentPage = Mathf.Min(_currentPage, totalPages - 1);
                _currentPage = Mathf.Max(0, _currentPage);
            }
        }

        private List<Song> GetCurrentPageSongs()
        {
            if (!_showPagination) return _filteredSongs;

            var startIndex = _currentPage * _songsPerPage;
            var count = Mathf.Min(_songsPerPage, _filteredSongs.Count - startIndex);

            if (startIndex >= _filteredSongs.Count) return new List<Song>();

            return _filteredSongs.GetRange(startIndex, count);
        }

        // Debug and utility methods
        private void DrawDebugInfo()
        {
            if (GUILayout.Button("Show Cache Stats"))
            {
                Debug.Log(SongDataEditorCache.GetCacheStats());
            }

            if (GUILayout.Button("Clear All Caches"))
            {
                SongDataEditorCache.ClearAllCaches();
                _needsRepaint = true;
                Debug.Log("All caches cleared.");
            }
        }

        private void DrawSongListWithPagination(SongData songData)
        {
            EditorGUILayout.BeginVertical("box");

            // Header with title and add button
            EditorGUILayout.BeginHorizontal();
            GUILayout.Label("Song List", EditorStyles.boldLabel);
            GUILayout.FlexibleSpace();
            if (GUILayout.Button("Add New Song", GUILayout.Width(100)))
            {
                AddNewSong(songData);
            }
            EditorGUILayout.EndHorizontal();

            var currentPageSongs = GetCurrentPageSongs();

            if (currentPageSongs.Count == 0)
            {
                GUILayout.Label("No songs found matching current filter.");
                EditorGUILayout.EndVertical();
                return;
            }

            // Display songs in current page
            for (int i = 0; i < currentPageSongs.Count; i++)
            {
                var song = currentPageSongs[i];
                var globalIndex = _filteredSongs.IndexOf(song);

                EditorGUILayout.BeginVertical("box");

                // Song header with expand/collapse
                EditorGUILayout.BeginHorizontal();
                var isExpanded = _expandedSongs.Contains(globalIndex);
                var newExpanded = EditorGUILayout.Foldout(isExpanded, $"#{(_currentPage * _songsPerPage) + i + 1}: [W{song.week}] {song.id}", true);

                if (newExpanded != isExpanded)
                {
                    if (newExpanded)
                        _expandedSongs.Add(globalIndex);
                    else
                        _expandedSongs.Remove(globalIndex);
                }

                // Quick info and actions
                GUILayout.FlexibleSpace();

                // Week display with background color
                var weekStyle = new GUIStyle(GUI.skin.label);
                weekStyle.normal.background = EditorGUIUtility.whiteTexture;
                weekStyle.normal.textColor = Color.black;
                weekStyle.fontStyle = FontStyle.Bold;
                weekStyle.alignment = TextAnchor.MiddleCenter;
                weekStyle.padding = new RectOffset(4, 4, 2, 2);

                GUILayout.Label($"W{song.week}", weekStyle, GUILayout.Width(35));
                GUILayout.Label($"{song.songName}", GUILayout.Width(120));

                // Include/Build status
                var buildStyle = new GUIStyle(GUI.skin.label);
                buildStyle.normal.textColor = song.isIncludeInBuild ? Color.green : Color.red;
                buildStyle.fontStyle = FontStyle.Bold;
                GUILayout.Label(song.isIncludeInBuild ? "✓" : "✗", buildStyle, GUILayout.Width(20));

                if (GUILayout.Button("Log Info", GUILayout.Width(60)))
                {
                    Debug.Log($"Song Info: {song.id}\n" +
                             $"Name: {song.songName}\n" +
                             $"Singer: {song.singer}\n" +
                             $"Week: {song.week}\n" +
                             $"WeekId: {song.weekId}\n" +
                             $"Include in Build: {song.isIncludeInBuild}");
                }
                EditorGUILayout.EndHorizontal();

                // Expanded content - editable fields
                if (newExpanded)
                {
                    EditorGUI.indentLevel++;
                    EditorGUILayout.BeginVertical("box");

                    // Editable fields with change detection
                    EditorGUI.BeginChangeCheck();

                    song.id = EditorGUILayout.TextField("ID", song.id);
                    song.songName = EditorGUILayout.TextField("Song Name", song.songName);
                    song.singer = EditorGUILayout.TextField("Singer", song.singer);
                    song.weekId = EditorGUILayout.TextField("Week ID", song.weekId);

                    EditorGUILayout.BeginHorizontal();
                    song.week = EditorGUILayout.IntField("Week", song.week, GUILayout.Width(200));
                    song.isIncludeInBuild = EditorGUILayout.Toggle("Include in Build", song.isIncludeInBuild);
                    EditorGUILayout.EndHorizontal();

                    EditorGUILayout.BeginHorizontal();
                    song.hasVocal = EditorGUILayout.Toggle("Has Vocal", song.hasVocal, GUILayout.Width(200));
                    song.isInstallTime = EditorGUILayout.Toggle("Install Time", song.isInstallTime);
                    EditorGUILayout.EndHorizontal();

                    EditorGUILayout.BeginHorizontal();
                    song.isFastFollow = EditorGUILayout.Toggle("Fast Follow", song.isFastFollow, GUILayout.Width(200));
                    song.bpm = EditorGUILayout.IntField("BPM", song.bpm);
                    EditorGUILayout.EndHorizontal();

                    // Icons
                    EditorGUILayout.BeginHorizontal();
                    song.myCharacterIcon = (Sprite)EditorGUILayout.ObjectField("My Character Icon", song.myCharacterIcon, typeof(Sprite), false);
                    song.opCharacterIcon = (Sprite)EditorGUILayout.ObjectField("Op Character Icon", song.opCharacterIcon, typeof(Sprite), false);
                    EditorGUILayout.EndHorizontal();

                    // Auto-save changes
                    if (EditorGUI.EndChangeCheck())
                    {
                        EditorUtility.SetDirty(songData);
                        song.name = song.GetName(); // Update display name
                    }

                    // Action buttons for this song
                    EditorGUILayout.BeginHorizontal();
                    if (GUILayout.Button("Update Name"))
                    {
                        song.name = song.GetName();
                        EditorUtility.SetDirty(songData);
                    }
                    if (GUILayout.Button("Duplicate Song"))
                    {
                        DuplicateSong(songData, song);
                    }
                    if (GUILayout.Button("Delete Song"))
                    {
                        if (EditorUtility.DisplayDialog("Delete Song", $"Are you sure you want to delete '{song.id}'?", "Delete", "Cancel"))
                        {
                            DeleteSong(songData, song);
                        }
                    }
                    EditorGUILayout.EndHorizontal();

                    EditorGUILayout.EndVertical();
                    EditorGUI.indentLevel--;
                }

                EditorGUILayout.EndVertical();
                GUILayout.Space(2);
            }

            EditorGUILayout.EndVertical();
        }

        private void DuplicateSong(SongData songData, Song originalSong)
        {
            var newSong = new Song();

            // Copy all properties
            newSong.id = originalSong.id + "_copy";
            newSong.songName = originalSong.songName + " (Copy)";
            newSong.singer = originalSong.singer;
            newSong.weekId = originalSong.weekId;
            newSong.week = originalSong.week;
            newSong.isIncludeInBuild = originalSong.isIncludeInBuild;
            newSong.hasVocal = originalSong.hasVocal;
            newSong.isInstallTime = originalSong.isInstallTime;
            newSong.isFastFollow = originalSong.isFastFollow;
            newSong.bpm = originalSong.bpm;
            newSong.myCharacterIcon = originalSong.myCharacterIcon;
            newSong.opCharacterIcon = originalSong.opCharacterIcon;

            // Add to song list
            var originalIndex = songData.listSong.IndexOf(originalSong);
            if (originalIndex >= 0)
            {
                songData.listSong.Insert(originalIndex + 1, newSong);
            }
            else
            {
                songData.listSong.Add(newSong);
            }

            // Update filtered list and refresh
            UpdateFilteredSongs(songData);
            EditorUtility.SetDirty(songData);
            _needsRepaint = true;

            Debug.Log($"Duplicated song: {originalSong.id} -> {newSong.id}");
        }

        private void DeleteSong(SongData songData, Song songToDelete)
        {
            var index = songData.listSong.IndexOf(songToDelete);
            if (index >= 0)
            {
                songData.listSong.RemoveAt(index);
                UpdateFilteredSongs(songData);
                EditorUtility.SetDirty(songData);
                _needsRepaint = true;

                // Clear expanded state for deleted song
                _expandedSongs.Remove(index);

                Debug.Log($"Deleted song: {songToDelete.id}");
            }
        }

        private void AddNewSong(SongData songData)
        {
            var newSong = new Song();
            newSong.id = "new_song_" + System.DateTime.Now.Ticks;
            newSong.songName = "New Song";
            newSong.singer = "Unknown Artist";
            newSong.weekId = "week_new";
            newSong.week = songData.listSong.Count > 0 ? songData.listSong.Max(s => s.week) + 1 : 0;
            newSong.isIncludeInBuild = true;
            newSong.hasVocal = true;
            newSong.bpm = 120;

            // Add to the end or after current page
            if (addNewSongToBottom)
            {
                songData.listSong.Add(newSong);
            }
            else
            {
                var insertIndex = (_currentPage + 1) * _songsPerPage;
                if (insertIndex > songData.listSong.Count)
                    insertIndex = songData.listSong.Count;
                songData.listSong.Insert(insertIndex, newSong);
            }

            UpdateFilteredSongs(songData);
            EditorUtility.SetDirty(songData);
            _needsRepaint = true;

            // Expand the new song for editing
            var newIndex = _filteredSongs.IndexOf(newSong);
            if (newIndex >= 0)
            {
                _expandedSongs.Add(newIndex);

                // Navigate to page containing the new song
                var newPage = newIndex / _songsPerPage;
                _currentPage = newPage;
            }

            Debug.Log($"Added new song: {newSong.id}");
        }

        // Background processing methods
        private static IEnumerator ProcessSongsInBackground<T>(List<T> items, System.Action<T, int, int> processAction, string operationName)
        {
            for (int i = 0; i < items.Count; i++)
            {
                EditorUtility.DisplayProgressBar(operationName, $"Processing item {i + 1}/{items.Count}", (float)i / items.Count);

                try
                {
                    processAction(items[i], i, items.Count);
                }
                catch (Exception e)
                {
                    Debug.LogError($"Error processing item {i}: {e.Message}");
                }

                // Yield every few items to keep UI responsive
                if (i % 5 == 0)
                    yield return null;
            }

            EditorUtility.ClearProgressBar();
        }

        private void ProcessSongsAsync(List<Song> songs, System.Action<Song, int, int> processAction, string operationName, System.Action onComplete = null)
        {
            EditorCoroutine.Start(ProcessSongsInBackgroundCoroutine(songs, processAction, operationName, onComplete));
        }

        private IEnumerator ProcessSongsInBackgroundCoroutine(List<Song> songs, System.Action<Song, int, int> processAction, string operationName, System.Action onComplete)
        {
            yield return ProcessSongsInBackground(songs, processAction, operationName);
            onComplete?.Invoke();
        }

        // Batch operations for better performance
        private void BatchUpdateSongs(SongData songData, System.Action<Song> updateAction, string operationName)
        {
            var currentPageSongs = GetCurrentPageSongs();
            var songsToProcess = currentPageSongs.Where(song => song.week >= startWeek && song.week < startWeek + numWeek).ToList();

            if (songsToProcess.Count == 0)
            {
                EditorUtility.DisplayDialog("No Songs", "No songs found in the current page/filter to process.", "OK");
                return;
            }

            ProcessSongsAsync(songsToProcess, (song, index, total) => updateAction(song), operationName, () =>
            {
                EditorUtility.SetDirty(songData);
                AssetDatabase.SaveAssets();
                _needsRepaint = true;
                Debug.Log($"{operationName} completed for {songsToProcess.Count} songs.");
            });
        }

        void MakeCloudUploader(SongData songData)
        {
            var songDataName = songData.name;
            Debug.Log("START MakeCloudUploader :: songDataName = " + songDataName);
            songData.ClearSongUrls();
            var cloudAssetSongs = new List<CloudAssetMap>();
            var iconAssetPaths = new List<string>();
            var listSong = songData.listSong;
            if(isNewSongOnly) listSong = songData.GetListSongNeedToAddDownloadLink();
            foreach (var song in listSong
            .Where(s => !s.isIncludeInBuild && s.week >= startWeek && s.week < startWeek + numWeek))
            {
                var fileKey = $"stages/{GetStorageFileKey(gameVersion, gamePackageId)}/{song.id}";
                var normal = new CloudAssetMap(song.id, song.id, song.songName,
                $"{Application.dataPath}/StageBundles/{song.id}",
                fileKey);
                Debug.Log(normal.AbsolutePrefabAssetPath);
                if (normal.ExistPrefab)
                {
                    song.SaveAssetUrl(this.storage.GetStorageUrl(fileKey), this.backupStorage.GetStorageUrl(fileKey), LevelController.GameDifficultMode.normal);
                    cloudAssetSongs.Add(normal);
                }

                fileKey = $"stages/{GetStorageFileKey(gameVersion, gamePackageId)}/{song.id}-easy";
                var easy = new CloudAssetMap(song.id + "-easy",song.id, song.songName,
                $"{Application.dataPath}/StageBundles/{song.id}-easy",
                fileKey);
                if (easy.ExistPrefab)
                {
                    song.SaveAssetUrl(this.storage.GetStorageUrl(fileKey), this.backupStorage.GetStorageUrl(fileKey), LevelController.GameDifficultMode.easy);
                    cloudAssetSongs.Add(easy);
                }

                fileKey = $"stages/{GetStorageFileKey(gameVersion, gamePackageId)}/{song.id}-hard";
                var hard = new CloudAssetMap(song.id + "-hard",song.id, song.songName,
                $"{Application.dataPath}/StageBundles/{song.id}-hard",
                fileKey);
                if (hard.ExistPrefab)
                {
                    song.SaveAssetUrl(this.storage.GetStorageUrl(fileKey), this.backupStorage.GetStorageUrl(fileKey), LevelController.GameDifficultMode.hard);
                    cloudAssetSongs.Add(hard);
                }

                var iconRootPath = $"stages/icons/{GetStorageFileKey(gameVersion, gamePackageId)}";

                song.bfIconAssetUrl = $"{this.storage.GetDescription()}/{iconRootPath}/{AssetDatabase.GetAssetPath(song.myCharacterIcon)}";
                song.opIconAssetUrl = $"{this.storage.GetDescription()}/{iconRootPath}/{AssetDatabase.GetAssetPath(song.opCharacterIcon)}";

                iconAssetPaths.Add($"{iconRootPath}/{AssetDatabase.GetAssetPath(song.myCharacterIcon)}");
                iconAssetPaths.Add($"{iconRootPath}/{AssetDatabase.GetAssetPath(song.myCharacterIcon)}.meta");
                iconAssetPaths.Add($"{iconRootPath}/{AssetDatabase.GetAssetPath(song.opCharacterIcon)}");
                iconAssetPaths.Add($"{iconRootPath}/{AssetDatabase.GetAssetPath(song.opCharacterIcon)}.meta");
            }

            cloudAssetSongs = cloudAssetSongs.Where(s => s.ExistPrefab).ToList();

            var uploaderPath = $"{Application.dataPath}/Resources/cloudUploader.json";
            if (File.Exists(uploaderPath))
            {
                File.Delete(uploaderPath);
            }
            var i = 0;
            foreach (var cloudAsset in cloudAssetSongs)
            {
                var song = songData.listSong.Find(s => s.id == cloudAsset.SongId);
                Debug.Log(cloudAsset.AbsolutePrefabAssetPath);
                if (cloudAsset.ExistPrefab && (isRebuildAssetBundle || !cloudAsset.ExistAssetBundle))
                {
                    if (string.IsNullOrEmpty(song.createdDate)) song.createdDate = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                    UpdateLevelData(cloudAsset, out bool hasVideoPlayerInGame);
                    MoveDialogTextOutOfResourceFolder(cloudAsset.PrefabAssetPath, cloudAsset.Id);

                    Debug.Log(cloudAsset.AssetBundleFolder);
                    if (!Directory.Exists(cloudAsset.AbsoluteAssetBundleFolder))
                    {
                        Debug.Log(cloudAsset.AbsoluteAssetBundleFolder);
                        Directory.CreateDirectory(cloudAsset.AbsoluteAssetBundleFolder);
                    }

                    // if (EditorUtility.DisplayCancelableProgressBar("Make Cloud Uploader", "Build asset bundle..." + cloudAsset.Id, i++ / (float)cloudAssetSongs.Count)) break;

                    var assetBundleMaker = AssetBundleMaker.Create(cloudAsset.PrefabAssetPath, cloudAsset.AssetBundleFolder);
                    AssetBundleMaker.SetCompression(assetBundleMaker, !hasVideoPlayerInGame)
                    .Build();

                    song.bundleFileSize = assetBundleMaker.bundleFileSize;
                    Debug.Log("Create map prefab from " + cloudAsset.PrefabAssetPath);
                }
                else
                {
                    Debug.LogError("Thieu map prefab : " + cloudAsset.PrefabAssetPath);
                }
            }

            foreach (var iconAssetPath in iconAssetPaths.Distinct())
            {
                var id = Path.GetFileName(iconAssetPath);
                var name = id;
                var absolutePath = $"{Application.dataPath}/{iconAssetPath.Substring(iconAssetPath.IndexOf("Assets/") + "Assets/".Length)}";
                var asset = new CloudAssetMap(id, id, name, absolutePath, iconAssetPath);
                cloudAssetSongs.Add(asset);
            }

            if(isIncludeSongData)
            {
                var fileKey = $"{songDataName}/{GetStorageFileKey(gameVersion, gamePackageId)}";
                CloudAssetMap cloudSongData = new CloudAssetMap(songDataName, songDataName, songDataName,
                $"{Application.dataPath}/StageBundles/{songDataName}",
                $"{songDataName}/{GetStorageFileKey(gameVersion, gamePackageId)}"
                );
                if (!Directory.Exists(cloudSongData.AbsoluteAssetBundleFolder))
                {
                    Debug.Log(cloudSongData.AbsoluteAssetBundleFolder);
                    Directory.CreateDirectory(cloudSongData.AbsoluteAssetBundleFolder);
                }

                Debug.Log(cloudSongData.AssetBundleFolder);
                songData.SetSongDataUrl(storage.GetStorageUrl(fileKey), backupStorage.GetStorageUrl(fileKey));
                AssetBundleMaker.Create($"Assets/Resources/{songDataName}.asset", cloudSongData.AssetBundleFolder)
                .Build();
                cloudAssetSongs.Add(cloudSongData);
            }
            SaveSongData();
            File.WriteAllText(uploaderPath, new CloudAssetMapCollection(cloudAssetSongs).ToJson());
            // EditorUtility.ClearProgressBar();
        }

    private void CheckSondataUrlExist(SongData songData)
    {
        error = "";
        //Check if song url file exist
        foreach (var song in songData.listSong)
        {
            if (song.week < startWeek || song.week >= startWeek + numWeek) continue;
            if (song.NeedToDownloadAsset)
            {
                foreach (var url in song.SongUrlCollection.AllUrls)
                {
                    if(!FileUtil.URLExists(url))
                    {
                        error += ("\nSong data error url khong ton tai: " + url);
                    }
                }
            }
        }
    }

    private void UpdateSongName(SongData songData)
    {
        foreach (var song in songData.listSong)
        {
            song.name = song.GetName();
        }
    }

    //Check if levelDatas name is same with songId
    private void CheckLevelDataError()
    {
        var songData = (SongData)target;
        error = "";
        foreach (var song in songData.listSong)
        {
            if (song.week < startWeek || song.week >= startWeek + numWeek) continue;
            var assetPath = FileUtil.GetPathExist(new string[]
            {
                "Assets/FNF/Resources/maps/" + song.id + ".prefab",
                "Assets/FNF/maps/" + song.id + ".prefab"
            });

            if(assetPath != "")
            {
                var mapData = AssetDatabase.LoadAssetAtPath<GameObject>(assetPath).GetComponent<MapData>();
                if(mapData.noteDatas.Count == 0)
                {
                    error +=("\nThieu level data " + song.id);
                    continue;
                }
                var songId = song.id.Split("_").Length > 1 ? song.id.Split("_")[1] : song.id;
                if(mapData.noteDatas[0].name.IndexOf(songId) != 0)
                {
                    error +=("\nLevel data name khac voi song id " + song.id);
                }
            }
        }
    }

    private void UpdatePlayAssetDelivery(SongData songData)
    {
        #if false
        {
            var _assetDeliveryConfig = AssetDeliveryConfigSerializer.LoadConfig();
            _assetDeliveryConfig.Refresh();
            Dictionary<string, bool> weekNotInBuild = new Dictionary<string, bool>();
            Dictionary<string, bool> weekInPlayAssetPack = new Dictionary<string, bool>();
            Dictionary<string, bool> weekInstallTime = new Dictionary<string, bool>();
            Dictionary<string, bool> weekFastFollow = new Dictionary<string, bool>();

            foreach (var song in songData.listSong)
            {
                if (!song.isIncludeInBuild)
                {
                    weekNotInBuild[song.weekId] = true;
                    weekInPlayAssetPack[song.weekId] = false;
                }
                if (song.isInstallTime) weekInstallTime[song.weekId] = true;
                if (song.isFastFollow) weekFastFollow[song.weekId] = true;
            }

            Debug.Log("weekNotInBuild");
            Debug.Log(string.Join(", ", weekNotInBuild));

            Debug.Log("weekInstallTime");
            Debug.Log(string.Join(", ", weekInstallTime));

            Debug.Log("weekFastFollow");
            Debug.Log(string.Join(", ", weekFastFollow));

            foreach (var bundle in _assetDeliveryConfig.AssetBundlePacks)
            {
                var deliveryMode = AssetPackDeliveryMode.DoNotPackage;
                if (weekNotInBuild.ContainsKey(bundle.Key) && weekNotInBuild[bundle.Key])
                {
                    deliveryMode = AssetPackDeliveryMode.OnDemand;
                    if (weekInstallTime.ContainsKey(bundle.Key) && weekInstallTime[bundle.Key]) deliveryMode = AssetPackDeliveryMode.InstallTime;
                    if (weekFastFollow.ContainsKey(bundle.Key) && weekFastFollow[bundle.Key]) deliveryMode = AssetPackDeliveryMode.FastFollow;
                }
                bundle.Value.DeliveryMode = deliveryMode;
                weekInPlayAssetPack[bundle.Key] = true;
            }

            Debug.Log("weekInPlayAssetPack " + string.Join(", ", weekInPlayAssetPack));

            foreach (var song in songData.listSong)
            {
                if (!song.isIncludeInBuild)
                {
                    if (!weekInPlayAssetPack.ContainsKey(song.weekId))
                    {
                        Debug.LogError("======= Thieu bundle " + song.weekId);
                    }
                }
            }

            int bundlesCount = weekNotInBuild.Count;
            Debug.Log("======= bundle count " + bundlesCount);

            if (bundlesCount > MAX_ASSET_BUNDLE)
            {
                Debug.LogError("======= ERROR : So luong bundle vuot qua so luong quy dinh " + bundlesCount);
                Debug.LogError(JsonConvert.SerializeObject(weekNotInBuild, Formatting.Indented));
            }
            AssetDeliveryConfigSerializer.SaveConfig(_assetDeliveryConfig);
        }
        #endif
    }

    private void MoveMapFolder(SongData songData)
    {
        //Move all map from Resources folder to maps folder
        FileUtil.MoveFolder("Assets/FNF/Resources/maps", "Assets/FNF/maps");

        foreach (var hardMode in hardModes)
        {
            foreach (var song in songData.listSong)
            {
                var assetPath = "Assets/FNF/Resources/maps/" + song.id + hardMode + ".prefab";
                if (System.IO.File.Exists(assetPath) && !song.isIncludeInBuild)
                {
                    var assetPath2 = "Assets/FNF/maps/" + song.id + hardMode + ".prefab";
                    FileUtil.MoveFile(assetPath, assetPath2);
                    FileUtil.MoveFile(assetPath + ".meta", assetPath2 + ".meta");
                    Debug.Log("Move " + assetPath + " to " + assetPath2);
                }
                else
                {
                    assetPath = "Assets/FNF/maps/" + song.id + hardMode + ".prefab";
                    if (System.IO.File.Exists(assetPath) && song.isIncludeInBuild)
                    {
                        var assetPath2 = "Assets/FNF/Resources/maps/" + song.id + hardMode + ".prefab";
                        FileUtil.MoveFile(assetPath, assetPath2);
                        FileUtil.MoveFile(assetPath + ".meta", assetPath2 + ".meta");
                        Debug.Log("Move " + assetPath + " to " + assetPath2);
                    }
                }
            }
        }
    }

    private void DrawLine()
    {
        EditorGUILayout.BeginHorizontal();
        GUILayout.FlexibleSpace();
        EditorGUILayout.LabelField("_______________________________________________________________________________");
        GUILayout.FlexibleSpace();
        EditorGUILayout.EndHorizontal();
    }

    private void UpdateLevelData(CloudAssetMap assetMap, out bool hasVideoPlayerInGame)
    {
        hasVideoPlayerInGame = false;
        var assetMapPath = assetMap.PrefabAssetPath;
        if (!System.IO.File.Exists(assetMapPath))
        {
            Debug.LogError("Khong tim thay map " + assetMapPath);
            return;
        }

        using (var editingScope = new PrefabUtility.EditPrefabContentsScope(assetMapPath))
        {
            var prefabRoot = editingScope.prefabContentsRoot;
            if( prefabRoot.GetComponent<MapData>() == null)
            {
                Debug.LogError("Khong tim thay MapData trong prefab " + assetMapPath);
                return;
            }
            if( prefabRoot.GetComponent<MapData>().noteDatas.Count >= 3)
            {
                return;
            }
            var noteDatas = new List<TextAsset>();
            for (int i = 0; i < (int)LevelController.GameDifficultMode.hell; i++)
            {
                var difficultMode = (LevelController.GameDifficultMode)i;
                var assetPath = "Assets/Resources/Level/" + assetMap.Id + (difficultMode == LevelController.GameDifficultMode.normal ? "" : "-" + difficultMode.ToString()) + ".json";
                if (!System.IO.File.Exists(assetPath))
                {
                    Debug.LogError("Thieu data level " + assetPath);
                    return;
                }
                noteDatas.Add(AssetDatabase.LoadAssetAtPath<TextAsset>(assetPath));
            }

            prefabRoot.GetComponent<MapData>().noteDatas = noteDatas;
            hasVideoPlayerInGame = prefabRoot.GetComponent<MapData>().videoPlayer != null;
        }
    }

    private IEnumerator SyncWithCloudSong()
    {
        var songData = (SongData)target;
        // var genericSongDataUrl = $"https://pub-d6f5eb52ee3645eea1acef1337e865e5.r2.dev/songData/com.game.fnfyoutube";
        var genericSongDataUrl = $"{this.storage.GetDescription()}/songData/{GetStorageFileKey(gameVersion, gamePackageId)}";

        var absoluteLocalFilePath = $"{Application.dataPath}/StageBundles/genericSongData";
        var editorStorage = new AndroidDeviceStorage();
        
        EditorHttpClient.DownloadFile(genericSongDataUrl, absoluteLocalFilePath);
        
        AssetDatabase.Refresh();

        AssetBundle.UnloadAllAssetBundles(true);
        var assetBundle = AssetBundle.LoadFromFile(absoluteLocalFilePath);
        foreach (var name in assetBundle.GetAllAssetNames())
        {
            Debug.Log("DOWNLOAD SongData COMPLETE " + name);
        }

        var targetAssetName = assetBundle.GetAllAssetNames().First(n => n.ToLower().Contains("songdata"));

        var cloudSongData = (SongData)assetBundle.LoadAssetAsync<SongData>(targetAssetName).asset;
        var newSongs = cloudSongData.listSong.Where(s => !songData.listSong.Any(d => d.id == s.id));
        Debug.Log(newSongs.Count());
        foreach(var newSong in newSongs)
        {
            var result = LoadIcon(newSong.bfIconAssetUrl, newSong.myCharacterIcon, 
            sprite => { newSong.myCharacterIcon = sprite; });
            while(result.MoveNext()) { yield return result; }

            result = LoadIcon(newSong.opIconAssetUrl, newSong.opCharacterIcon, 
            sprite => { newSong.opCharacterIcon = sprite; });
            while(result.MoveNext()) { yield return result; }
        }
        if (addNewSongToBottom)
        {
            songData.listSong.AddRange(newSongs);
        }
        else 
        {
            songData.listSong.InsertRange(0, newSongs);
        }

        assetBundle.Unload(true);

        EditorUtility.SetDirty(songData);
        AssetDatabase.SaveAssets();
    }

    private static string GetStorageFileKey(string gameVersion, string appId)
    {
        var buildTarget = EditorUserBuildSettings.activeBuildTarget;
        var fileKey = $"{ShortGameVersion()}/{appId}/{buildTarget}";

        return fileKey;

        string ShortGameVersion()
        {
            var versions = gameVersion.Split('.');
            return string.Join(".", versions.Take(2));
        }
    }


    IEnumerator LoadIcon(string iconAssetUrl, Sprite rootIcon, Action<Sprite> onSuccess)
    {
        if (string.IsNullOrEmpty(iconAssetUrl) || rootIcon == null) 
        yield break;

        var iconAssetMetaUrl = iconAssetUrl + ".meta";
        Debug.Log(iconAssetUrl);
        var absoluteLocalAssetMetaPath = $"{Application.dataPath}/{iconAssetMetaUrl.Substring(iconAssetUrl.IndexOf("Assets/") + "Assets/".Length)}";

        var iconLocalAssetPath = iconAssetUrl.Substring(iconAssetUrl.IndexOf("Assets/"));
        var absoluteLocalAssetPath = $"{Application.dataPath}/{iconAssetUrl.Substring(iconAssetUrl.IndexOf("Assets/") + "Assets/".Length)}";
        Debug.Log($"Download asset: " + iconAssetUrl + ", " + absoluteLocalAssetPath);

        if (rootIcon == null || string.IsNullOrEmpty(iconAssetUrl) || File.Exists(absoluteLocalAssetPath))
            yield return true;

        var iconImages = UnityEditor.AssetDatabase.LoadAllAssetsAtPath(iconLocalAssetPath);
        if (iconImages == null || iconImages.Length == 0)
        {
            var result = DownloadAsset(iconAssetUrl, absoluteLocalAssetPath);
            while(result.MoveNext()) { yield return result; }

            result = DownloadAsset(iconAssetMetaUrl, absoluteLocalAssetMetaPath);
            while(result.MoveNext()) { yield return result; }

            Debug.Log($"Downloaded asset successfully: " + iconAssetUrl + ", save to: " + absoluteLocalAssetPath);
            AssetDatabase.Refresh();

            iconImages = AssetDatabase.LoadAllAssetsAtPath(iconLocalAssetPath);
        }

        Debug.Log("Create sprite from local: " + iconImages.Length + ", " + GetSpriteIndex(rootIcon.name) + ", " + rootIcon.name);
        var imageSprite = iconImages.First(i => i.name == rootIcon.name);
        if (!(imageSprite is Sprite))
        {
            imageSprite = iconImages.Where(i => i is Sprite).FirstOrDefault();
        }
        if (imageSprite != null)
        {
            onSuccess.Invoke((Sprite)imageSprite);
        }
        else 
        {
            Debug.LogError("Could not load sprite from image " + iconLocalAssetPath);
        }

        yield return true;

        int GetSpriteIndex(string spriteName)
        {
            var pieces = spriteName.Split('_');
            if (pieces.Length > 1) 
            {
                try
                {
                    var index = Convert.ToInt32(pieces[1]);
                    return index;
                }
                catch (Exception e)
                {
                    Debug.LogError("ERROR :: icon sprite incorrect format :: " + spriteName);
                }
            }

            return 0;
        }

        IEnumerator DownloadAsset(string dataUrl, string absoluteLocalFilePath, Action onSuccess = null)
        {
            if (File.Exists(absoluteLocalAssetPath))
            {
                onSuccess?.Invoke();
                yield return true;
            }
            
            EditorHttpClient.DownloadFile(dataUrl, absoluteLocalFilePath);
            yield return true;
        }
    }

    private void CheckWeekRelationship(SongData songData)
    {
        Debug.Log("--------Checking week relationship-------");
        var weeksRelationships = songData.weeksRelationships;
        var listSong = songData.listSong;

        List<WeekInfo> weeksInfo = new List<WeekInfo>();
        var maxWeek = Mathf.Min(listSong[listSong.Count - 1].week, GameConfig.MAX_WEEK_SHOW_IN_GAME);
        for (int i = 0; i <= maxWeek; i++)
        {
            var listWeekSong = listSong.FindAll(s => s.week == i);
            if (listWeekSong.Count > 0)
            {
                var song = listWeekSong[0];
                weeksInfo.Add(new WeekInfo() { 
                    week = song.week, weekId = song.weekId, weekName = song.singer,
                    modId = song.modId == "" ? song.weekId : song.modId,
                    modName = song.modName == "" ? song.singer : song.modName,
                    iconOP = song.opCharacterIcon });
            }
        }

        List<WeekRelationship> listWeekRelationships = new List<WeekRelationship>();
        for (var i = 0; i < weeksInfo.Count; i++)
        {
            var week = weeksInfo[i];
            var weekRelationship = songData.weeksRelationships == null ? null : songData.weeksRelationships.Find(w => w.weekId       == week.weekId);
            if (weekRelationship == null)
            {
                weekRelationship = new WeekRelationship()
                {
                    weekId = week.weekId,
                    nextWeeksId = i < weeksInfo.Count - 1 ? new string[] { weeksInfo[i + 1].weekId } : new string[] { },
                    previousWeeksId = i > 0 ? new string[] { weeksInfo[i - 1].weekId } : new string[] { },
                    nextWeeksIdCanUnlock = i < weeksInfo.Count - 1 ? new string[] { weeksInfo[i + 1].weekId } : new string[] { },
                };
            }
            Array.Sort(weekRelationship.nextWeeksId, (a, b) => weeksInfo.Find(w => w.weekId == a).week - weeksInfo.Find(w => w.weekId == b).week);
            listWeekRelationships.Add(weekRelationship);
        }

        var listWeeksIdCanUnlock = new List<string>();
        listWeekRelationships.ForEach(w => {
            listWeeksIdCanUnlock.AddRange(w.nextWeeksIdCanUnlock);
        });

        var listWeeksCanNotUnlock = weeksInfo.FindAll(w => !listWeeksIdCanUnlock.Contains(w.weekId));
        listWeeksCanNotUnlock.ForEach(w => {
            if (w.week != 0) Debug.LogError("ERROR :: Has week can not be unlocked :: weekName = " + w.weekName + " -- weekId = " + w.weekId);
        });
        Debug.Log("--------Checking week relationship DONE-------");
        error = songData.GetSongDataError();
    }

    private void MoveDialogTextOutOfResourceFolder(string assetPath, string songId)
    {
        try
        {
            using (var editingScope = new PrefabUtility.EditPrefabContentsScope(assetPath))
            {
                var prefabRoot = editingScope.prefabContentsRoot;
                var mapData = prefabRoot.GetComponent<MapData>();
                var animations = new List<GameObject>();
                animations.AddRange(mapData.listEndAnimation);
                animations.AddRange(mapData.listLoseAnimation);
                animations.AddRange(mapData.listStartAnimation);
                animations = animations.FindAll(a => a.GetComponent<Dialog>() != null && a.GetComponent<Dialog>().dialogTextAsset == null);
                animations.ForEach(a =>
                {
                    var dialog = a.GetComponent<Dialog>();
                    var dialogAssetPath = "Assets/FNF/Resources/maps/" + (dialog.dialogTextId != "" ? dialog.dialogTextId : songId + "Dialogue") + ".txt";
                    var dialogMoveAssetPath = "Assets/FNF/dialogText/" + (dialog.dialogTextId != "" ? dialog.dialogTextId : songId + "Dialogue") + ".txt";
                    if (!File.Exists(dialogAssetPath))
                    {
                        dialogAssetPath = "Assets/FNF/Resources/maps/" + (dialog.gameObject.name) + ".txt";
                        dialogMoveAssetPath = "Assets/FNF/dialogText/" + (dialog.gameObject.name) + ".txt";
                    }
                    if (dialog != null && dialog.dialogTextAsset == null) dialog.dialogTextAsset = AssetDatabase.LoadAssetAtPath<TextAsset>(dialogAssetPath);
                    if (File.Exists(dialogAssetPath)) AssetDatabase.MoveAsset(dialogAssetPath, dialogMoveAssetPath);
                });
            }
        }
        catch (Exception e) { Debug.LogError("Error edit song " + songId + " : " + e.Message); }
    }
}