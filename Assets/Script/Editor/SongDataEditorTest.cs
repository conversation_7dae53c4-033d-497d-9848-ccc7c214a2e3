using UnityEngine;
using UnityEditor;

/// <summary>
/// Test script to verify SongDataEditor optimizations are working
/// </summary>
public class SongDataEditorTest : EditorWindow
{
    [MenuItem("Tools/FNF/Test SongDataEditor Performance")]
    static void TestPerformance()
    {
        Debug.Log("=== SongDataEditor Performance Test ===");
        
        // Test cache functionality
        Debug.Log("Testing cache functionality...");
        var stats = SongDataEditorCache.GetCacheStats();
        Debug.Log("Initial cache stats:\n" + stats);
        
        // Test file existence caching
        var testPath = "Assets/FNF/Resources/maps/test.prefab";
        var startTime = System.DateTime.Now;
        
        for (int i = 0; i < 100; i++)
        {
            SongDataEditorCache.FileExists(testPath);
        }
        
        var endTime = System.DateTime.Now;
        var duration = (endTime - startTime).TotalMilliseconds;
        
        Debug.Log($"100 file existence checks took: {duration}ms");
        Debug.Log("Cache stats after test:\n" + SongDataEditorCache.GetCacheStats());
        
        // Clear cache test
        SongDataEditorCache.ClearAllCaches();
        Debug.Log("Cache cleared. Final stats:\n" + SongDataEditorCache.GetCacheStats());
        
        Debug.Log("=== Performance Test Complete ===");
    }
    
    [MenuItem("Tools/FNF/Clear SongDataEditor Cache")]
    static void ClearCache()
    {
        SongDataEditorCache.ClearAllCaches();
        Debug.Log("SongDataEditor cache cleared!");
    }
    
    [MenuItem("Tools/FNF/Show Cache Stats")]
    static void ShowCacheStats()
    {
        Debug.Log("SongDataEditor Cache Stats:\n" + SongDataEditorCache.GetCacheStats());
    }

    [MenuItem("Tools/FNF/Test Pagination")]
    static void TestPagination()
    {
        var songData = Resources.FindObjectsOfTypeAll<SongData>();
        if (songData.Length > 0)
        {
            var data = songData[0];
            Debug.Log($"Found SongData with {data.listSong.Count} songs");
            Debug.Log("Pagination should now be working in the inspector!");
            Debug.Log("Look for search box and page controls at the top of the SongData inspector.");
        }
        else
        {
            Debug.LogWarning("No SongData found in the project. Create or select a SongData asset to test pagination.");
        }
    }
}
