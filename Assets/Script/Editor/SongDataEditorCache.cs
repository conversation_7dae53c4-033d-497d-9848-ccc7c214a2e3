using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEditor;

/// <summary>
/// Cache manager for SongDataEditor to improve performance
/// </summary>
public static class SongDataEditorCache
{
    private static Dictionary<string, bool> _fileExistenceCache = new Dictionary<string, bool>();
    private static Dictionary<string, MapData> _mapDataCache = new Dictionary<string, MapData>();
    private static Dictionary<string, AudioClip> _audioClipCache = new Dictionary<string, AudioClip>();
    private static Dictionary<string, TextAsset> _textAssetCache = new Dictionary<string, TextAsset>();
    private static Dictionary<string, Sprite> _spriteCache = new Dictionary<string, Sprite>();
    private static Dictionary<string, GameObject> _gameObjectCache = new Dictionary<string, GameObject>();
    
    private static double _lastCacheCleanup = 0;
    private const double CACHE_CLEANUP_INTERVAL = 30.0; // seconds
    private const int MAX_CACHE_SIZE = 1000;
    
    /// <summary>
    /// Check if file exists with caching
    /// </summary>
    public static bool FileExists(string path)
    {
        if (_fileExistenceCache.TryGetValue(path, out bool exists))
            return exists;
            
        exists = System.IO.File.Exists(path);
        
        // Prevent cache from growing too large
        if (_fileExistenceCache.Count >= MAX_CACHE_SIZE)
        {
            var keysToRemove = _fileExistenceCache.Keys.Take(MAX_CACHE_SIZE / 2).ToList();
            foreach (var key in keysToRemove)
                _fileExistenceCache.Remove(key);
        }
        
        _fileExistenceCache[path] = exists;
        return exists;
    }
    
    /// <summary>
    /// Load asset with caching
    /// </summary>
    public static T LoadAsset<T>(string path) where T : UnityEngine.Object
    {
        Dictionary<string, T> cache = GetCacheForType<T>();
        
        if (cache.TryGetValue(path, out T asset) && asset != null)
            return asset;
            
        asset = AssetDatabase.LoadAssetAtPath<T>(path);
        
        // Prevent cache from growing too large
        if (cache.Count >= MAX_CACHE_SIZE)
        {
            var keysToRemove = cache.Keys.Take(MAX_CACHE_SIZE / 2).ToList();
            foreach (var key in keysToRemove)
                cache.Remove(key);
        }
        
        if (asset != null)
            cache[path] = asset;
            
        return asset;
    }
    
    /// <summary>
    /// Get map asset path with caching
    /// </summary>
    public static string GetMapAssetPath(string songId)
    {
        var cacheKey = $"mapPath_{songId}";
        if (_fileExistenceCache.TryGetValue(cacheKey, out bool cached) && cached)
        {
            // Return the first path that exists
            var path1 = "Assets/FNF/Resources/maps/" + songId + ".prefab";
            if (FileExists(path1)) return path1;
            
            var path2 = "Assets/FNF/maps/" + songId + ".prefab";
            if (FileExists(path2)) return path2;
        }
        
        // Check paths and cache result
        var assetPath = "Assets/FNF/Resources/maps/" + songId + ".prefab";
        if (FileExists(assetPath))
        {
            _fileExistenceCache[cacheKey] = true;
            return assetPath;
        }
        
        assetPath = "Assets/FNF/maps/" + songId + ".prefab";
        if (FileExists(assetPath))
        {
            _fileExistenceCache[cacheKey] = true;
            return assetPath;
        }
        
        _fileExistenceCache[cacheKey] = false;
        return string.Empty;
    }
    
    /// <summary>
    /// Get music asset paths with caching
    /// </summary>
    public static string[] GetMusicAssetPaths(string songId)
    {
        var instPath = FileUtil.GetPathExist(new string[]
        {
            "Assets/MusicData/" + songId + "/Inst.mp3",
            "Assets/MusicData/" + songId + "/Inst.ogg",
            "Assets/MusicData/" + songId + "_Inst.mp3",
            "Assets/MusicData/" + songId + "_Inst.ogg",
            "Assets/MusicData/" + songId + ".mp3",
            "Assets/MusicData/" + songId + ".ogg",
        });
        
        var voicesPath = FileUtil.GetPathExist(new string[]
        {
            "Assets/MusicData/" + songId + "/Voices.mp3",
            "Assets/MusicData/" + songId + "/Voices.ogg",
            "Assets/MusicData/" + songId + "_Voices.mp3",
            "Assets/MusicData/" + songId + "_Voices.ogg",
        });
        
        var voiceDataPath = FileUtil.GetPathExist(new string[]
        {
            "Assets/FNF/LevelVoice/" + songId + ".json",
            "Assets/FNF/LevelVoice/" + songId + "_Voices.json"
        });
        
        return new string[] { instPath, voicesPath, voiceDataPath };
    }
    
    /// <summary>
    /// Clean up cache periodically
    /// </summary>
    public static void CleanupCache()
    {
        var currentTime = EditorApplication.timeSinceStartup;
        if (currentTime - _lastCacheCleanup > CACHE_CLEANUP_INTERVAL)
        {
            _lastCacheCleanup = currentTime;
            
            // Clean up null references
            CleanupNullReferences(_mapDataCache);
            CleanupNullReferences(_audioClipCache);
            CleanupNullReferences(_textAssetCache);
            CleanupNullReferences(_spriteCache);
            CleanupNullReferences(_gameObjectCache);
        }
    }
    
    /// <summary>
    /// Clear all caches
    /// </summary>
    public static void ClearAllCaches()
    {
        _fileExistenceCache.Clear();
        _mapDataCache.Clear();
        _audioClipCache.Clear();
        _textAssetCache.Clear();
        _spriteCache.Clear();
        _gameObjectCache.Clear();
    }
    
    /// <summary>
    /// Get cache statistics
    /// </summary>
    public static string GetCacheStats()
    {
        return $"Cache Stats:\n" +
               $"File Existence: {_fileExistenceCache.Count}\n" +
               $"MapData: {_mapDataCache.Count}\n" +
               $"AudioClip: {_audioClipCache.Count}\n" +
               $"TextAsset: {_textAssetCache.Count}\n" +
               $"Sprite: {_spriteCache.Count}\n" +
               $"GameObject: {_gameObjectCache.Count}";
    }
    
    private static Dictionary<string, T> GetCacheForType<T>() where T : UnityEngine.Object
    {
        if (typeof(T) == typeof(MapData))
            return _mapDataCache as Dictionary<string, T>;
        if (typeof(T) == typeof(AudioClip))
            return _audioClipCache as Dictionary<string, T>;
        if (typeof(T) == typeof(TextAsset))
            return _textAssetCache as Dictionary<string, T>;
        if (typeof(T) == typeof(Sprite))
            return _spriteCache as Dictionary<string, T>;
        if (typeof(T) == typeof(GameObject))
            return _gameObjectCache as Dictionary<string, T>;
            
        // Fallback to a generic cache
        return new Dictionary<string, T>();
    }
    
    private static void CleanupNullReferences<T>(Dictionary<string, T> cache) where T : UnityEngine.Object
    {
        var nullKeys = cache.Where(kvp => kvp.Value == null).Select(kvp => kvp.Key).ToList();
        foreach (var key in nullKeys)
            cache.Remove(key);
    }
}
