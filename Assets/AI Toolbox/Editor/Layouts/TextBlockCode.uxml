<ui:UXML xmlns:ui="UnityEngine.UIElements" xmlns:uie="UnityEditor.UIElements" xsi="http://www.w3.org/2001/XMLSchema-instance" engine="UnityEngine.UIElements" editor="UnityEditor.UIElements" noNamespaceSchemaLocation="../../../../../../UIElementsSchema/UIElements.xsd" editor-extension-mode="True">
    <ui:Label text="Label" display-tooltip-when-elided="true" name="text" style="white-space: normal; -unity-font-style: normal; -unity-font: resource(&apos;RobotoMono-VariableFont&apos;); -unity-font-definition: none; border-left-width: 3px; border-right-width: 3px; border-top-width: 3px; border-bottom-width: 3px; border-top-left-radius: 5px; border-bottom-left-radius: 5px; border-top-right-radius: 5px; border-bottom-right-radius: 5px; border-left-color: rgb(32, 32, 32); border-right-color: rgb(32, 32, 32); border-top-color: rgb(32, 32, 32); border-bottom-color: rgb(32, 32, 32); background-color: rgb(41, 41, 41); margin-top: 5px;" />
    <ui:VisualElement name="buttons-container" style="flex-direction: row; justify-content: flex-end; position: absolute; width: 50px; right: 3px; top: 8px;">
        <ui:IMGUIContainer name="buttons-imgui" style="flex-grow: 1; height: 16px;" />
    </ui:VisualElement>
</ui:UXML>
