<ui:UXML xmlns:ui="UnityEngine.UIElements" xmlns:uie="UnityEditor.UIElements" editor-extension-mode="True">
    <ui:VisualElement name="chat-message-container" style="flex-grow: 1; background-color: rgba(0, 0, 0, 0); transition-duration: 0.5s; transition-timing-function: ease-in-out; flex-direction: row; flex-shrink: 0; justify-content: space-around; padding-left: 10px; padding-right: 10px; padding-top: 10px; padding-bottom: 10px;">
        <ui:IMGUIContainer name="message-icon-imgui" style="flex-grow: 0; max-width: 40px; min-width: 40px;" />
        <ui:VisualElement name="right-panel" style="flex-grow: 1; background-color: rgba(0, 0, 0, 0);">
            <ui:VisualElement name="message-container" style="flex-grow: 1; flex-shrink: 1; min-height: 30px;" />
            <ui:IMGUIContainer name="message-footer-imgui" style="height: 20px; align-self: flex-end; position: relative; width: 20px;" />
        </ui:VisualElement>
    </ui:VisualElement>
</ui:UXML>
