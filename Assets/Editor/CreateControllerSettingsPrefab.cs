using UnityEngine;
using UnityEngine.UI;
using UnityEditor;
using System.IO;
using TMPro;

public class CreateControllerSettingsPrefab
{
    [MenuItem("Tools/Create Controller Settings Prefab")]
    public static void CreatePrefab()
    {
        // Create the prefab path
        string prefabPath = "Assets/Resources/Prefabs";

        // Create the directory if it doesn't exist
        if (!Directory.Exists(prefabPath))
        {
            Directory.CreateDirectory(prefabPath);
        }

        // Create a new GameObject with the ControllerSettingsUI component
        GameObject controllerSettingsObj = new GameObject("ControllerSettingsUI");
        controllerSettingsObj.AddComponent<RectTransform>();
        Canvas canvas = controllerSettingsObj.AddComponent<Canvas>();
        canvas.renderMode = RenderMode.ScreenSpaceOverlay;
        controllerSettingsObj.AddComponent<CanvasScaler>();
        controllerSettingsObj.AddComponent<GraphicRaycaster>();
        ControllerSettingsUI controllerSettingsUI = controllerSettingsObj.AddComponent<ControllerSettingsUI>();

        // Create the panel
        GameObject panel = new GameObject("ControllerSettingsPanel", typeof(RectTransform), typeof(CanvasRenderer), typeof(Image));
        RectTransform rectTransform = panel.GetComponent<RectTransform>();
        rectTransform.SetParent(controllerSettingsObj.transform);
        rectTransform.anchorMin = new Vector2(0.5f, 0.5f);
        rectTransform.anchorMax = new Vector2(0.5f, 0.5f);
        rectTransform.pivot = new Vector2(0.5f, 0.5f);
        rectTransform.anchoredPosition = Vector2.zero;
        rectTransform.sizeDelta = new Vector2(800, 600);
        rectTransform.localScale = Vector3.one;

        // Set panel background
        Image panelImage = panel.GetComponent<Image>();
        panelImage.color = new Color(0.1f, 0.1f, 0.1f, 0.9f);

        // Create title
        GameObject titleObj = new GameObject("Title", typeof(RectTransform), typeof(Text));
        RectTransform titleRect = titleObj.GetComponent<RectTransform>();
        titleRect.SetParent(panel.transform);
        titleRect.anchorMin = new Vector2(0.5f, 1f);
        titleRect.anchorMax = new Vector2(0.5f, 1f);
        titleRect.pivot = new Vector2(0.5f, 1f);
        titleRect.anchoredPosition = new Vector2(0, -50);
        titleRect.sizeDelta = new Vector2(700, 80);
        titleRect.localScale = Vector3.one;

        Text titleText = titleObj.GetComponent<Text>();
        titleText.text = "CONTROLLER SETTINGS";
        titleText.fontSize = 36;
        titleText.alignment = TextAnchor.MiddleCenter;
        titleText.color = Color.white;

        // Create controller toggle
        GameObject toggleObj = new GameObject("EnableControllerToggle", typeof(RectTransform), typeof(Toggle));
        RectTransform toggleRect = toggleObj.GetComponent<RectTransform>();
        toggleRect.SetParent(panel.transform);
        toggleRect.anchorMin = new Vector2(0.5f, 1f);
        toggleRect.anchorMax = new Vector2(0.5f, 1f);
        toggleRect.pivot = new Vector2(0.5f, 1f);
        toggleRect.anchoredPosition = new Vector2(0, -150);
        toggleRect.sizeDelta = new Vector2(40, 40);
        toggleRect.localScale = Vector3.one;

        Toggle toggle = toggleObj.GetComponent<Toggle>();

        // Create toggle background
        GameObject toggleBg = new GameObject("Background", typeof(RectTransform), typeof(Image));
        RectTransform toggleBgRect = toggleBg.GetComponent<RectTransform>();
        toggleBgRect.SetParent(toggleObj.transform);
        toggleBgRect.anchorMin = Vector2.zero;
        toggleBgRect.anchorMax = Vector2.one;
        toggleBgRect.sizeDelta = Vector2.zero;
        toggleBgRect.localScale = Vector3.one;

        Image toggleBgImage = toggleBg.GetComponent<Image>();
        toggleBgImage.color = Color.white;

        // Create toggle checkmark
        GameObject checkmark = new GameObject("Checkmark", typeof(RectTransform), typeof(Image));
        RectTransform checkmarkRect = checkmark.GetComponent<RectTransform>();
        checkmarkRect.SetParent(toggleObj.transform);
        checkmarkRect.anchorMin = new Vector2(0.5f, 0.5f);
        checkmarkRect.anchorMax = new Vector2(0.5f, 0.5f);
        checkmarkRect.pivot = new Vector2(0.5f, 0.5f);
        checkmarkRect.anchoredPosition = Vector2.zero;
        checkmarkRect.sizeDelta = new Vector2(30, 30);
        checkmarkRect.localScale = Vector3.one;

        Image checkmarkImage = checkmark.GetComponent<Image>();
        checkmarkImage.color = Color.green;

        toggle.graphic = checkmarkImage;
        toggle.targetGraphic = toggleBgImage;

        // Create toggle label
        GameObject labelObj = new GameObject("Label", typeof(RectTransform), typeof(Text));
        RectTransform labelRect = labelObj.GetComponent<RectTransform>();
        labelRect.SetParent(toggleObj.transform);
        labelRect.anchorMin = new Vector2(1f, 0.5f);
        labelRect.anchorMax = new Vector2(1f, 0.5f);
        labelRect.pivot = new Vector2(0f, 0.5f);
        labelRect.anchoredPosition = new Vector2(10, 0);
        labelRect.sizeDelta = new Vector2(300, 40);
        labelRect.localScale = Vector3.one;

        Text labelText = labelObj.GetComponent<Text>();
        labelText.text = "Enable Controller";
        labelText.fontSize = 24;
        labelText.alignment = TextAnchor.MiddleLeft;
        labelText.color = Color.white;

        // Create controller status text
        GameObject statusObj = new GameObject("ControllerStatus", typeof(RectTransform), typeof(Text));
        RectTransform statusRect = statusObj.GetComponent<RectTransform>();
        statusRect.SetParent(panel.transform);
        statusRect.anchorMin = new Vector2(0.5f, 1f);
        statusRect.anchorMax = new Vector2(0.5f, 1f);
        statusRect.pivot = new Vector2(0.5f, 1f);
        statusRect.anchoredPosition = new Vector2(0, -220);
        statusRect.sizeDelta = new Vector2(700, 40);
        statusRect.localScale = Vector3.one;

        Text statusText = statusObj.GetComponent<Text>();
        statusText.text = "Controller: Not Connected";
        statusText.fontSize = 24;
        statusText.alignment = TextAnchor.MiddleCenter;
        statusText.color = Color.red;

        // Create lane indicators container
        GameObject container = new GameObject("LaneButtonIndicators", typeof(RectTransform), typeof(HorizontalLayoutGroup));
        RectTransform containerRect = container.GetComponent<RectTransform>();
        containerRect.SetParent(panel.transform);
        containerRect.anchorMin = new Vector2(0.5f, 0.5f);
        containerRect.anchorMax = new Vector2(0.5f, 0.5f);
        containerRect.pivot = new Vector2(0.5f, 0.5f);
        containerRect.anchoredPosition = Vector2.zero;
        containerRect.sizeDelta = new Vector2(600, 100);
        containerRect.localScale = Vector3.one;

        HorizontalLayoutGroup layout = container.GetComponent<HorizontalLayoutGroup>();
        layout.spacing = 20;
        layout.childAlignment = TextAnchor.MiddleCenter;
        layout.childForceExpandWidth = false;
        layout.childForceExpandHeight = false;

        // Create lane indicators
        string[] laneNames = new string[] { "LEFT", "DOWN", "UP", "RIGHT" };
        Color[] laneColors = new Color[] {
            new Color(0.8f, 0.2f, 0.2f, 1f), // Red for Left
            new Color(0.2f, 0.2f, 0.8f, 1f), // Blue for Down
            new Color(0.2f, 0.8f, 0.2f, 1f), // Green for Up
            new Color(0.8f, 0.8f, 0.2f, 1f)  // Yellow for Right
        };

        GameObject[] indicators = new GameObject[4];

        for (int i = 0; i < 4; i++)
        {
            GameObject indicator = new GameObject("Lane" + i, typeof(RectTransform), typeof(Image));
            RectTransform indicatorRect = indicator.GetComponent<RectTransform>();
            indicatorRect.SetParent(container.transform);
            indicatorRect.sizeDelta = new Vector2(100, 100);
            indicatorRect.localScale = Vector3.one;

            Image indicatorImage = indicator.GetComponent<Image>();
            indicatorImage.color = laneColors[i];

            // Create lane label
            GameObject laneLabelObj = new GameObject("Label", typeof(RectTransform), typeof(Text));
            RectTransform laneLabelRect = laneLabelObj.GetComponent<RectTransform>();
            laneLabelRect.SetParent(indicator.transform);
            laneLabelRect.anchorMin = Vector2.zero;
            laneLabelRect.anchorMax = Vector2.one;
            laneLabelRect.sizeDelta = Vector2.zero;
            laneLabelRect.localScale = Vector3.one;

            Text laneLabelText = laneLabelObj.GetComponent<Text>();
            laneLabelText.text = laneNames[i];
            laneLabelText.fontSize = 20;
            laneLabelText.alignment = TextAnchor.MiddleCenter;
            laneLabelText.color = Color.white;

            indicators[i] = indicator;
        }

        // Create close button
        GameObject closeBtn = new GameObject("CloseButton", typeof(RectTransform), typeof(Image), typeof(Button));
        RectTransform closeBtnRect = closeBtn.GetComponent<RectTransform>();
        closeBtnRect.SetParent(panel.transform);
        closeBtnRect.anchorMin = new Vector2(0.5f, 0f);
        closeBtnRect.anchorMax = new Vector2(0.5f, 0f);
        closeBtnRect.pivot = new Vector2(0.5f, 0f);
        closeBtnRect.anchoredPosition = new Vector2(0, 50);
        closeBtnRect.sizeDelta = new Vector2(200, 60);
        closeBtnRect.localScale = Vector3.one;

        Image closeBtnImage = closeBtn.GetComponent<Image>();
        closeBtnImage.color = new Color(0.2f, 0.2f, 0.2f, 1f);

        // Create button text
        GameObject btnTextObj = new GameObject("Text", typeof(RectTransform), typeof(Text));
        RectTransform btnTextRect = btnTextObj.GetComponent<RectTransform>();
        btnTextRect.SetParent(closeBtn.transform);
        btnTextRect.anchorMin = Vector2.zero;
        btnTextRect.anchorMax = Vector2.one;
        btnTextRect.sizeDelta = Vector2.zero;
        btnTextRect.localScale = Vector3.one;

        Text btnText = btnTextObj.GetComponent<Text>();
        btnText.text = "CLOSE";
        btnText.fontSize = 24;
        btnText.alignment = TextAnchor.MiddleCenter;
        btnText.color = Color.white;

        // Assign references
        controllerSettingsUI.controllerSettingsPanel = panel;
        controllerSettingsUI.enableControllerToggle = toggle;
        controllerSettingsUI.controllerStatusText = statusText.GetComponent<Text>();
        controllerSettingsUI.laneButtonIndicators = indicators;

        // Create the prefab
        string fullPath = Path.Combine(prefabPath, "ControllerSettingsPanel.prefab");

        // Check if the prefab already exists
        bool prefabExists = File.Exists(fullPath);

        if (prefabExists)
        {
            // Update the existing prefab
            GameObject existingPrefab = AssetDatabase.LoadAssetAtPath<GameObject>(fullPath);
            PrefabUtility.SaveAsPrefabAsset(controllerSettingsObj, fullPath);
            Debug.Log("Controller Settings Prefab updated at: " + fullPath);
        }
        else
        {
            // Create a new prefab
            PrefabUtility.SaveAsPrefabAsset(controllerSettingsObj, fullPath);
            Debug.Log("Controller Settings Prefab created at: " + fullPath);
        }

        // Destroy the temporary GameObject
        Object.DestroyImmediate(controllerSettingsObj);

        // Refresh the asset database
        AssetDatabase.Refresh();
    }
}
