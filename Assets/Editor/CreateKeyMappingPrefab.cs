using UnityEngine;
using UnityEngine.UI;
using UnityEditor;
using System.IO;

public class CreateKeyMappingPrefab
{
    [MenuItem("Tools/Create Key Mapping Prefab")]
    public static void CreatePrefab()
    {
        // Create the prefab path
        string prefabPath = "Assets/Resources/Prefabs";

        // Create the directory if it doesn't exist
        if (!Directory.Exists(prefabPath))
        {
            Directory.CreateDirectory(prefabPath);
        }

        // Create a new GameObject with the ControllerKeyMappingUI component
        GameObject keyMappingObj = new GameObject("KeyMappingUI");
        keyMappingObj.AddComponent<RectTransform>();
        Canvas canvas = keyMappingObj.AddComponent<Canvas>();
        canvas.renderMode = RenderMode.ScreenSpaceOverlay;
        canvas.sortingOrder = 10; // Make sure it appears on top
        keyMappingObj.AddComponent<CanvasScaler>();
        keyMappingObj.AddComponent<GraphicRaycaster>();

        // Add tag for easy finding
        keyMappingObj.tag = "KeyMappingPanel";

        // Create the panel
        GameObject panel = new GameObject("KeyMappingPanel", typeof(RectTransform), typeof(CanvasRenderer), typeof(Image));
        RectTransform rectTransform = panel.GetComponent<RectTransform>();
        rectTransform.SetParent(keyMappingObj.transform);
        rectTransform.anchorMin = new Vector2(0.5f, 0.5f);
        rectTransform.anchorMax = new Vector2(0.5f, 0.5f);
        rectTransform.pivot = new Vector2(0.5f, 0.5f);
        rectTransform.anchoredPosition = Vector2.zero;
        rectTransform.sizeDelta = new Vector2(600, 500);
        rectTransform.localScale = Vector3.one;

        // Set panel background
        Image panelImage = panel.GetComponent<Image>();
        panelImage.color = new Color(0.1f, 0.1f, 0.1f, 0.9f);

        // Create title
        GameObject titleObj = new GameObject("Title", typeof(RectTransform), typeof(Text));
        RectTransform titleRect = titleObj.GetComponent<RectTransform>();
        titleRect.SetParent(panel.transform);
        titleRect.anchorMin = new Vector2(0.5f, 1f);
        titleRect.anchorMax = new Vector2(0.5f, 1f);
        titleRect.pivot = new Vector2(0.5f, 1f);
        titleRect.anchoredPosition = new Vector2(0, -40);
        titleRect.sizeDelta = new Vector2(500, 60);
        titleRect.localScale = Vector3.one;

        Text titleText = titleObj.GetComponent<Text>();
        titleText.text = "KEYBOARD CONTROLLER MAPPING";
        titleText.fontSize = 24;
        titleText.alignment = TextAnchor.MiddleCenter;
        titleText.color = Color.white;

        // Create status text
        GameObject statusObj = new GameObject("StatusText", typeof(RectTransform), typeof(Text));
        RectTransform statusRect = statusObj.GetComponent<RectTransform>();
        statusRect.SetParent(panel.transform);
        statusRect.anchorMin = new Vector2(0.5f, 1f);
        statusRect.anchorMax = new Vector2(0.5f, 1f);
        statusRect.pivot = new Vector2(0.5f, 1f);
        statusRect.anchoredPosition = new Vector2(0, -100);
        statusRect.sizeDelta = new Vector2(500, 40);
        statusRect.localScale = Vector3.one;

        Text statusText = statusObj.GetComponent<Text>();
        statusText.text = "Press a button to remap";
        statusText.fontSize = 18;
        statusText.alignment = TextAnchor.MiddleCenter;
        statusText.color = Color.white;

        // Create lane mapping container
        GameObject container = new GameObject("LaneContainer", typeof(RectTransform), typeof(VerticalLayoutGroup));
        RectTransform containerRect = container.GetComponent<RectTransform>();
        containerRect.SetParent(panel.transform);
        containerRect.anchorMin = new Vector2(0.5f, 0.5f);
        containerRect.anchorMax = new Vector2(0.5f, 0.5f);
        containerRect.pivot = new Vector2(0.5f, 0.5f);
        containerRect.anchoredPosition = new Vector2(0, 0);
        containerRect.sizeDelta = new Vector2(500, 250);
        containerRect.localScale = Vector3.one;

        VerticalLayoutGroup layout = container.GetComponent<VerticalLayoutGroup>();
        layout.spacing = 15;
        layout.childAlignment = TextAnchor.MiddleCenter;
        layout.childForceExpandWidth = true;
        layout.childForceExpandHeight = false;
        layout.padding = new RectOffset(20, 20, 10, 10);

        // Create lane mapping rows
        string[] laneNames = new string[] { "LEFT", "DOWN", "UP", "RIGHT" };
        Text[] laneLabels = new Text[4];
        Button[] remapButtons = new Button[4];

        for (int i = 0; i < 4; i++)
        {
            // Create row container
            GameObject row = new GameObject("Lane" + i + "Row", typeof(RectTransform), typeof(HorizontalLayoutGroup));
            RectTransform rowRect = row.GetComponent<RectTransform>();
            rowRect.SetParent(container.transform);
            rowRect.sizeDelta = new Vector2(460, 50);
            rowRect.localScale = Vector3.one;

            HorizontalLayoutGroup rowLayout = row.GetComponent<HorizontalLayoutGroup>();
            rowLayout.spacing = 10;
            rowLayout.childAlignment = TextAnchor.MiddleLeft;
            rowLayout.childForceExpandWidth = false;
            rowLayout.childForceExpandHeight = true;

            // Create lane name
            GameObject laneNameObj = new GameObject("LaneName", typeof(RectTransform), typeof(Text));
            RectTransform laneNameRect = laneNameObj.GetComponent<RectTransform>();
            laneNameRect.SetParent(row.transform);
            laneNameRect.sizeDelta = new Vector2(100, 40);

            Text laneNameText = laneNameObj.GetComponent<Text>();
            laneNameText.text = laneNames[i];
            laneNameText.fontSize = 18;
            laneNameText.alignment = TextAnchor.MiddleLeft;
            laneNameText.color = Color.white;

            // Create key label
            GameObject keyLabelObj = new GameObject("KeyLabel", typeof(RectTransform), typeof(Text));
            RectTransform keyLabelRect = keyLabelObj.GetComponent<RectTransform>();
            keyLabelRect.SetParent(row.transform);
            keyLabelRect.sizeDelta = new Vector2(150, 40);

            Text keyLabelText = keyLabelObj.GetComponent<Text>();
            keyLabelText.text = "Not Set";
            keyLabelText.fontSize = 18;
            keyLabelText.alignment = TextAnchor.MiddleCenter;
            keyLabelText.color = Color.yellow;
            laneLabels[i] = keyLabelText;

            // Create remap button
            GameObject remapButtonObj = new GameObject("RemapButton", typeof(RectTransform), typeof(Image), typeof(Button));
            RectTransform remapButtonRect = remapButtonObj.GetComponent<RectTransform>();
            remapButtonRect.SetParent(row.transform);
            remapButtonRect.sizeDelta = new Vector2(120, 40);

            Image remapButtonImage = remapButtonObj.GetComponent<Image>();
            remapButtonImage.color = new Color(0.3f, 0.3f, 0.3f, 1f);

            // Create button text
            GameObject buttonTextObj = new GameObject("Text", typeof(RectTransform), typeof(Text));
            RectTransform buttonTextRect = buttonTextObj.GetComponent<RectTransform>();
            buttonTextRect.SetParent(remapButtonObj.transform);
            buttonTextRect.anchorMin = Vector2.zero;
            buttonTextRect.anchorMax = Vector2.one;
            buttonTextRect.sizeDelta = Vector2.zero;

            Text buttonText = buttonTextObj.GetComponent<Text>();
            buttonText.text = "REMAP";
            buttonText.fontSize = 16;
            buttonText.alignment = TextAnchor.MiddleCenter;
            buttonText.color = Color.white;

            Button remapButton = remapButtonObj.GetComponent<Button>();
            remapButtons[i] = remapButton;

            // We'll connect the button handlers in ControllerKeyMappingUI.Start
        }

        // Create buttons container
        GameObject buttonsContainer = new GameObject("ButtonsContainer", typeof(RectTransform), typeof(HorizontalLayoutGroup));
        RectTransform buttonsContainerRect = buttonsContainer.GetComponent<RectTransform>();
        buttonsContainerRect.SetParent(panel.transform);
        buttonsContainerRect.anchorMin = new Vector2(0.5f, 0f);
        buttonsContainerRect.anchorMax = new Vector2(0.5f, 0f);
        buttonsContainerRect.pivot = new Vector2(0.5f, 0f);
        buttonsContainerRect.anchoredPosition = new Vector2(0, 50);
        buttonsContainerRect.sizeDelta = new Vector2(500, 60);

        HorizontalLayoutGroup buttonsLayout = buttonsContainer.GetComponent<HorizontalLayoutGroup>();
        buttonsLayout.spacing = 20;
        buttonsLayout.childAlignment = TextAnchor.MiddleCenter;
        buttonsLayout.childForceExpandWidth = false;
        buttonsLayout.childForceExpandHeight = true;

        // Create reset button
        GameObject resetBtn = new GameObject("ResetButton", typeof(RectTransform), typeof(Image), typeof(Button));
        RectTransform resetBtnRect = resetBtn.GetComponent<RectTransform>();
        resetBtnRect.SetParent(buttonsContainer.transform);
        resetBtnRect.sizeDelta = new Vector2(150, 50);

        Image resetBtnImage = resetBtn.GetComponent<Image>();
        resetBtnImage.color = new Color(0.8f, 0.2f, 0.2f, 1f);

        // Create reset button text
        GameObject resetBtnTextObj = new GameObject("Text", typeof(RectTransform), typeof(Text));
        RectTransform resetBtnTextRect = resetBtnTextObj.GetComponent<RectTransform>();
        resetBtnTextRect.SetParent(resetBtn.transform);
        resetBtnTextRect.anchorMin = Vector2.zero;
        resetBtnTextRect.anchorMax = Vector2.one;
        resetBtnTextRect.sizeDelta = Vector2.zero;

        Text resetBtnText = resetBtnTextObj.GetComponent<Text>();
        resetBtnText.text = "RESET";
        resetBtnText.fontSize = 18;
        resetBtnText.alignment = TextAnchor.MiddleCenter;
        resetBtnText.color = Color.white;

        // We'll connect the button handlers in ControllerKeyMappingUI.Start

        // Create close button
        GameObject closeBtn = new GameObject("CloseButton", typeof(RectTransform), typeof(Image), typeof(Button));
        RectTransform closeBtnRect = closeBtn.GetComponent<RectTransform>();
        closeBtnRect.SetParent(buttonsContainer.transform);
        closeBtnRect.sizeDelta = new Vector2(150, 50);

        Image closeBtnImage = closeBtn.GetComponent<Image>();
        closeBtnImage.color = new Color(0.2f, 0.2f, 0.2f, 1f);

        // Create close button text
        GameObject closeBtnTextObj = new GameObject("Text", typeof(RectTransform), typeof(Text));
        RectTransform closeBtnTextRect = closeBtnTextObj.GetComponent<RectTransform>();
        closeBtnTextRect.SetParent(closeBtn.transform);
        closeBtnTextRect.anchorMin = Vector2.zero;
        closeBtnTextRect.anchorMax = Vector2.one;
        closeBtnTextRect.sizeDelta = Vector2.zero;

        Text closeBtnText = closeBtnTextObj.GetComponent<Text>();
        closeBtnText.text = "CLOSE";
        closeBtnText.fontSize = 18;
        closeBtnText.alignment = TextAnchor.MiddleCenter;
        closeBtnText.color = Color.white;

        // We'll connect the button handlers in ControllerKeyMappingUI.Start

        // Add the ControllerKeyMappingUI component
        var keyMappingUI = keyMappingObj.AddComponent<ControllerKeyMappingUI>();

        // Use public methods to set fields
        keyMappingUI.SetKeyMappingPanel(panel);
        keyMappingUI.SetStatusText(statusText);
        keyMappingUI.SetLaneButtonLabels(laneLabels);
        keyMappingUI.SetRemapButtons(remapButtons);

        // Create the prefab
        string fullPath = Path.Combine(prefabPath, "KeyMappingPanel.prefab");

        // Check if the prefab already exists
        bool prefabExists = File.Exists(fullPath);

        if (prefabExists)
        {
            // Update the existing prefab
            GameObject existingPrefab = AssetDatabase.LoadAssetAtPath<GameObject>(fullPath);
            PrefabUtility.SaveAsPrefabAsset(keyMappingObj, fullPath);
            Debug.Log("Key Mapping Prefab updated at: " + fullPath);
        }
        else
        {
            // Create a new prefab
            PrefabUtility.SaveAsPrefabAsset(keyMappingObj, fullPath);
            Debug.Log("Key Mapping Prefab created at: " + fullPath);
        }

        // Destroy the temporary GameObject
        Object.DestroyImmediate(keyMappingObj);

        // Refresh the asset database
        AssetDatabase.Refresh();
    }
}
