using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System;

public class InputManagerSetup
{
    [MenuItem("Tools/Setup Controller Input")]
    public static void SetupControllerInput()
    {
        // Get the serialized object of the input manager
        SerializedObject inputManager = new SerializedObject(AssetDatabase.LoadAllAssetsAtPath("ProjectSettings/InputManager.asset")[0]);

        // Get the axes array
        SerializedProperty axesProperty = inputManager.FindProperty("m_Axes");

        // Define the controller buttons we need
        string[] buttonNames = new string[]
        {
            "joystick button 0",  // A/X
            "joystick button 1",  // B/Circle
            "joystick button 2",  // X/Square
            "joystick button 3",  // Y/Triangle
            "joystick button 4",  // LB/L1
            "joystick button 5",  // RB/R1
            "joystick button 6",  // Back/Select
            "joystick button 7",  // Start
            "joystick button 8",  // Left Stick Press
            "joystick button 9",  // Right Stick Press
            "joystick button 10", // Guide/<PERSON> But<PERSON>
            "joystick button 11", // D-Pad Up
            "joystick button 12", // D-Pad Right
            "joystick button 13", // D-Pad Down
            "joystick button 14", // D-Pad Left
            "joystick button 15", // Extra button 1
            "joystick button 16", // Extra button 2
            "joystick button 17", // Extra button 3
            "joystick button 18", // Extra button 4
            "joystick button 19"  // Extra button 5
        };

        // Define the special Xbox controller axes we need
        List<AxisDefinition> axisDefinitions = new List<AxisDefinition>()
        {
            // Xbox D-pad specific axes
            new AxisDefinition("XboxDPadHorizontal", "Xbox D-Pad Horizontal", "", 2, 6, false),
            new AxisDefinition("XboxDPadVertical", "Xbox D-Pad Vertical", "", 2, 7, false),

            // Additional axes that might be used for D-pad on different controllers
            new AxisDefinition("3rd axis", "3rd axis", "", 2, 2, false),
            new AxisDefinition("4th axis", "4th axis", "", 2, 3, false),
            new AxisDefinition("5th axis", "5th axis", "", 2, 4, false),
            new AxisDefinition("6th axis", "6th axis", "", 2, 5, false),
            new AxisDefinition("7th axis", "7th axis", "", 2, 6, false),
            new AxisDefinition("8th axis", "8th axis", "", 2, 7, false),
            new AxisDefinition("9th axis", "9th axis", "", 2, 8, false),
        };

        // Check if buttons already exist
        List<string> existingInputs = new List<string>();
        for (int i = 0; i < axesProperty.arraySize; i++)
        {
            SerializedProperty axis = axesProperty.GetArrayElementAtIndex(i);
            string name = axis.FindPropertyRelative("m_Name").stringValue;
            existingInputs.Add(name);
        }

        // Add missing buttons
        foreach (string buttonName in buttonNames)
        {
            if (!existingInputs.Contains(buttonName))
            {
                // Add a new axis
                axesProperty.arraySize++;
                SerializedProperty axis = axesProperty.GetArrayElementAtIndex(axesProperty.arraySize - 1);

                // Set the properties
                axis.FindPropertyRelative("m_Name").stringValue = buttonName;
                axis.FindPropertyRelative("descriptiveName").stringValue = buttonName;
                axis.FindPropertyRelative("descriptiveNegativeName").stringValue = "";
                axis.FindPropertyRelative("negativeButton").stringValue = "";
                axis.FindPropertyRelative("positiveButton").stringValue = buttonName;
                axis.FindPropertyRelative("altNegativeButton").stringValue = "";
                axis.FindPropertyRelative("altPositiveButton").stringValue = "";
                axis.FindPropertyRelative("gravity").floatValue = 1000f;
                axis.FindPropertyRelative("dead").floatValue = 0.001f;
                axis.FindPropertyRelative("sensitivity").floatValue = 1000f;
                axis.FindPropertyRelative("snap").boolValue = false;
                axis.FindPropertyRelative("invert").boolValue = false;
                axis.FindPropertyRelative("type").intValue = 0; // Button
                axis.FindPropertyRelative("axis").intValue = 0;
                axis.FindPropertyRelative("joyNum").intValue = 0; // All joysticks

                Debug.Log("Added button input: " + buttonName);
            }
        }

        // Add missing axes
        foreach (AxisDefinition axisDef in axisDefinitions)
        {
            if (!existingInputs.Contains(axisDef.name))
            {
                // Add a new axis
                axesProperty.arraySize++;
                SerializedProperty axis = axesProperty.GetArrayElementAtIndex(axesProperty.arraySize - 1);

                // Set the properties
                axis.FindPropertyRelative("m_Name").stringValue = axisDef.name;
                axis.FindPropertyRelative("descriptiveName").stringValue = axisDef.descriptiveName;
                axis.FindPropertyRelative("descriptiveNegativeName").stringValue = axisDef.descriptiveNegativeName;
                axis.FindPropertyRelative("negativeButton").stringValue = "";
                axis.FindPropertyRelative("positiveButton").stringValue = "";
                axis.FindPropertyRelative("altNegativeButton").stringValue = "";
                axis.FindPropertyRelative("altPositiveButton").stringValue = "";
                axis.FindPropertyRelative("gravity").floatValue = 0f;
                axis.FindPropertyRelative("dead").floatValue = 0.19f;
                axis.FindPropertyRelative("sensitivity").floatValue = 1f;
                axis.FindPropertyRelative("snap").boolValue = false;
                axis.FindPropertyRelative("invert").boolValue = axisDef.invert;
                axis.FindPropertyRelative("type").intValue = axisDef.type; // Joystick axis
                axis.FindPropertyRelative("axis").intValue = axisDef.axis;
                axis.FindPropertyRelative("joyNum").intValue = 0; // All joysticks

                Debug.Log("Added axis input: " + axisDef.name);
            }
        }

        // Apply the changes
        inputManager.ApplyModifiedProperties();

        Debug.Log("Controller input setup complete!");
    }

    // Class to define an axis
    private class AxisDefinition
    {
        public string name;
        public string descriptiveName;
        public string descriptiveNegativeName;
        public int type; // 0 = key/button, 1 = mouse movement, 2 = joystick axis
        public int axis;
        public bool invert;

        public AxisDefinition(string name, string descriptiveName, string descriptiveNegativeName, int type, int axis, bool invert)
        {
            this.name = name;
            this.descriptiveName = descriptiveName;
            this.descriptiveNegativeName = descriptiveNegativeName;
            this.type = type;
            this.axis = axis;
            this.invert = invert;
        }
    }
}
