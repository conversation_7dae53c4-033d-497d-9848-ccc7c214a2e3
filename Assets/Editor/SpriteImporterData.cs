using System;
using System.Collections.Generic;
using System.IO;
using Prankard.FlashSpriteSheetImporter;
using UnityEditor;
using UnityEngine;

public class SpriteImporterData
{
    static List<string> notOptimizedFiles = new List<string>();
    private static string errorFilePath = Application.dataPath + "/FNF/SpriteImporterDataError.txt";

    public static void AddNotOptimizedFile(string fileName)
    {
        if(fileName.Contains("Assets/FNF/SongData/Icons/")) return;
        LoadFileTextData();
        if(!notOptimizedFiles.Contains(fileName))
        {
            notOptimizedFiles.Add(fileName);
        }
        SaveFileTextData();
    }

    public static void RemoveNotOptimizedFile(string fileName)
    {
        LoadFileTextData();
        if(notOptimizedFiles.Contains(fileName))
        {
            notOptimizedFiles.Remove(fileName);
        }
        SaveFileTextData();
    }

    static void LoadFileTextData()
    {
        var textFile = File.Exists(errorFilePath) ? File.ReadAllText(errorFilePath) : "";
        var rawPaths = textFile != "" ? textFile.Split(new char[] { '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries) : new string[0];

        notOptimizedFiles = new List<string>();
        foreach (var path in rawPaths)
        {
            var cleanPath = path.Trim();
            if (IsValidPath(cleanPath))
            {
                notOptimizedFiles.Add(cleanPath);
            }
            else if (!string.IsNullOrEmpty(cleanPath))
            {
                Debug.LogWarning("Skipping corrupted path: " + cleanPath);
            }
        }
    }

    static void SaveFileTextData()
    {
        var text = string.Join("\n", notOptimizedFiles.ToArray());
        File.WriteAllText(errorFilePath, text);
    }

    static bool IsValidPath(string path)
    {
        if (string.IsNullOrEmpty(path)) return false;

        // Check if path starts with Assets/
        if (!path.StartsWith("Assets/")) return false;

        // Check for invalid characters that might indicate corruption
        if (path.Contains("//") || path.Contains("\\\\")) return false;

        // Check for repeated segments that might indicate corruption
        var segments = path.Split('/');
        for (int i = 0; i < segments.Length - 1; i++)
        {
            for (int j = i + 1; j < segments.Length; j++)
            {
                if (segments[i] == segments[j] && segments[i].Length > 3)
                {
                    // Found repeated segment, might be corruption
                    return false;
                }
            }
        }

        // Check if path has valid extension
        var extension = Path.GetExtension(path);
        if (string.IsNullOrEmpty(extension) || (!extension.Equals(".png", StringComparison.OrdinalIgnoreCase) &&
            !extension.Equals(".jpg", StringComparison.OrdinalIgnoreCase) &&
            !extension.Equals(".jpeg", StringComparison.OrdinalIgnoreCase)))
        {
            return false;
        }

        return true;
    }

    [MenuItem("Tools/FNF/Fix Images Size")]
    public static void FixImagesImport()
    {
        LoadFileTextData();
        foreach (var path in notOptimizedFiles)
        {
            FixImage(path);
        }
    }

    [MenuItem("Tools/FNF/Fix Images Size by atlas")]
    public static void FixImagesImportByAtlas()
    {
        LoadFileTextData();
        foreach (var path in notOptimizedFiles)
        {
            if (FixImage(path, true))
            {
                // return;
            }
        }
    }

    [MenuItem("Tools/FNF/Fix Images Size by resize")]
    public static void FixImagesImportByReSize()
    {
        LoadFileTextData();
        foreach (var path in notOptimizedFiles)
        {
            if(path.Contains("Assets/FNF/SongData/Icons/"))
            {
                Debug.Log("Khong fix cho file icon : " + path);
                RemoveNotOptimizedFile(path);
                continue;
            }
            if (FixImage(path, false, true))
            {
                // return;
            }
        }
    }

    [MenuItem("Tools/FNF/Clean Corrupted Paths")]
    public static void CleanCorruptedPaths()
    {
        LoadFileTextData();
        var originalCount = notOptimizedFiles.Count;
        var validPaths = new List<string>();

        foreach (var path in notOptimizedFiles)
        {
            if (IsValidPath(path))
            {
                validPaths.Add(path);
            }
            else
            {
                Debug.LogWarning("Removing corrupted path: " + path);
            }
        }

        notOptimizedFiles = validPaths;
        SaveFileTextData();

        var removedCount = originalCount - notOptimizedFiles.Count;
        Debug.Log($"Cleaned {removedCount} corrupted paths. {notOptimizedFiles.Count} valid paths remaining.");
    }

    public static bool CheckIfErrorImportFile()
    {
        LoadFileTextData();
        return notOptimizedFiles.Count > 0;
    }

    public static bool FixImage(string path, bool isForceAtlas = false, bool isForceResize = false)
    {
        if (string.IsNullOrEmpty(path)) return false;
        path = path.Trim();

        // Validate path before processing
        if (!IsValidPath(path))
        {
            Debug.LogWarning("Invalid or corrupted path detected, removing from list: " + path);
            RemoveNotOptimizedFile(path);
            return false;
        }

        var fileName = Path.GetFileNameWithoutExtension(path);
        var extension = Path.GetExtension(path);
        var foundPath = FileUtil.GetPathExist(new string[]{
                path,
                Path.GetDirectoryName(path) + "/" + fileName + "_skip." + extension,
            });
        if (foundPath == "")
        {
            Debug.Log("Khong tim thay file : " + path);
            RemoveNotOptimizedFile(path);
            return false;
        }
        var atlasPath = Path.GetDirectoryName(path) + "/" + Path.GetFileNameWithoutExtension(path) + ".spriteatlasv2";
        if (File.Exists(atlasPath))
        {
            RemoveNotOptimizedFile(path);
            Debug.Log("Da ton tai file atlas : " + path);
            return true;
        }
        var xmlPath = Path.GetDirectoryName(path) + "/" + Path.GetFileNameWithoutExtension(path) + ".xml";
        if (File.Exists(xmlPath) && !isForceAtlas && !isForceResize)
        {
            Debug.Log("Da ton tai file xml nhung khong tao ra atlas => khong dung file :" + path);
            return false;
        }
        //Ignore pixel file path
        if (path.IndexOf("pixel") >= 0)
        {
            Debug.Log("Khong fix cho anh pixel : " + path);
            RemoveNotOptimizedFile(path);
            return false;
        }

        if(isForceResize)
        {
            return AnimationCreator.ResizeImage(path);
        }

        var objs = AssetDatabase.LoadAllAssetsAtPath(path);
        var sprites = new List<Sprite>();
        foreach (var obj in objs)
        {
            if(obj != null){
                var sprite = obj as Sprite;
                sprites.Add(sprite);
            }
        }


        if(isForceAtlas && sprites.Count == 1)
        {
            Debug.Log("Do not fix this because there is only one sprite in this file : " + path);
            return false;
        }

        Debug.Log("Tao atlas cho : " + path);
        AnimationCreator.CreateAtlas(path, sprites, false);
        RemoveNotOptimizedFile(path);
        return true;
    }
}
