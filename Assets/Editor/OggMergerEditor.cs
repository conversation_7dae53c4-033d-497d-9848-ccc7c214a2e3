using UnityEngine;
using UnityEditor;
using System.IO;
using System.Diagnostics;

public class OggMixerEditor : EditorWindow
{
    private Object oggFile1;
    private Object oggFile2;

    [MenuItem("Tools/Mix OGG Files (Overlay)")]
    public static void ShowWindow()
    {
        GetWindow<OggMixerEditor>("Mix OGG Files");
    }

    private void OnGUI()
    {
        GUILayout.Label("Mix 2 OGG files (overlay/audio blend)", EditorStyles.boldLabel);

        oggFile1 = EditorGUILayout.ObjectField("OGG File 1", oggFile1, typeof(Object), false);
        oggFile2 = EditorGUILayout.ObjectField("OGG File 2", oggFile2, typeof(Object), false);

        if (GUILayout.Button("Mix OGG Files"))
        {
            MixOggFiles();
        }
    }

    private void MixOggFiles()
    {
        if (oggFile1 == null || oggFile2 == null)
        {
            EditorUtility.DisplayDialog("Error", "Please assign 2 .ogg files.", "OK");
            return;
        }

        string path1 = AssetDatabase.GetAssetPath(oggFile1);
        string path2 = AssetDatabase.GetAssetPath(oggFile2);

        string fullPath1 = Path.GetFullPath(path1);
        string fullPath2 = Path.GetFullPath(path2);

        if (!fullPath1.EndsWith(".ogg") || !fullPath2.EndsWith(".ogg"))
        {
            EditorUtility.DisplayDialog("Error", "Both files must be .ogg format.", "OK");
            return;
        }

        string directory = Path.GetDirectoryName(fullPath1);
        string outputPath = Path.Combine(directory, "mixed_output.ogg");

        ProcessStartInfo psi = new ProcessStartInfo();
        psi.FileName = "/opt/homebrew/bin/ffmpeg"; // ← Thay bằng đường dẫn `ffmpeg` nếu khác
        psi.Arguments = $"-i \"{fullPath1}\" -i \"{fullPath2}\" -filter_complex \"amix=inputs=2:duration=longest:dropout_transition=2\" -c:a libvorbis \"{outputPath}\"";
        psi.CreateNoWindow = true;
        psi.UseShellExecute = false;
        psi.RedirectStandardOutput = true;
        psi.RedirectStandardError = true;
        psi.EnvironmentVariables["PATH"] = "/opt/homebrew/bin:" + System.Environment.GetEnvironmentVariable("PATH");

        try
        {
            Process process = Process.Start(psi);
            string error = process.StandardError.ReadToEnd();
            process.WaitForExit();

            if (File.Exists(outputPath))
            {
                EditorUtility.DisplayDialog("Success", "OGG files mixed successfully!", "OK");
                AssetDatabase.Refresh();
            }
            else
            {
                EditorUtility.DisplayDialog("Failed", $"Failed to mix files.\n{error}", "OK");
            }
        }
        catch (System.Exception ex)
        {
            EditorUtility.DisplayDialog("Exception", ex.Message, "OK");
        }
    }
}
