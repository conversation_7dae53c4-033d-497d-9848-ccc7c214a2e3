using UnityEngine;
using UnityEditor;

[CustomEditor(typeof(GachaMaster))]
public class GachaMasterEditor : Editor
{
    public override void OnInspectorGUI()
    {
        DrawDefaultInspector();

        GachaMaster gachaMaster = (GachaMaster)target;
        if (GUILayout.Button("Update Weights (AutoConfig)"))
        {
            gachaMaster.UpdateWeights();
            EditorUtility.SetDirty(gachaMaster);
            Debug.Log("Auto updated weights for " + gachaMaster.name);
        }
    }
} 