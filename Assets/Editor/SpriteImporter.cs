﻿// #define SKIP_IMAGE_IMPORT
using System.IO;
using UnityEditor;
using UnityEngine;
using Prankard.FlashSpriteSheetImporter;
using System.Reflection;
using System.Collections.Generic;
#if SKIP_IMAGE_IMPORT
public class SpriteImporter{
    void OnPreprocessTexture(){}
    void OnPostprocessTexture(Texture2D texture){}
}
#else
public class SpriteImporter : AssetPostprocessor
{
    public const int maxSize = 4096;
    public const int maxSizeEditor = 512;
    public static TextureImporterFormat format = TextureImporterFormat.ETC2_RGBA8Crunched;
    public static TextureImporterFormat formatPixel = TextureImporterFormat.RGBA16;
    public static TextureImporterFormat formatJpg = TextureImporterFormat.ETC2_RGBA8Crunched;
    void OnPreprocessTexture()
    {
        TextureImporter textureImporter = (TextureImporter)assetImporter;
        // Debug.Log("Textutre name  " + textureImporter.assetPath);
        string name = textureImporter.assetPath.ToLower();
        if(!assetPath.StartsWith("Assets")) return;
        CheckTextureSize(textureImporter, assetPath);
        if (name.IndexOf("_skip") >= 0) return;
        textureImporter.SetPlatformTextureSettings("Standalone", maxSizeEditor, textureImporter.GetPlatformTextureSettings("Standalone").format);
        textureImporter.maxTextureSize = maxSize;
        if(textureImporter.isReadable) textureImporter.isReadable = false;

        var fileExtendtion = name.Substring(name.Length - 4, 4);
        //"_cf = custom format"
        if (name.IndexOf("_cf") >= 0) {}
        else if(name.IndexOf("pixel") >= 0)
        {
            textureImporter.SetPlatformTextureSettings("Android",maxSize, formatPixel);
            textureImporter.filterMode = UnityEngine.FilterMode.Point;
            textureImporter.SetPlatformTextureSettings("iPhone", maxSize, formatPixel);
        }
        else if (fileExtendtion == ".jpg")
        {
            textureImporter.compressionQuality = 100;
            textureImporter.SetPlatformTextureSettings("Android", maxSize, formatJpg);
            textureImporter.SetPlatformTextureSettings("iPhone", maxSize, formatJpg);
        }
        else if ((fileExtendtion == ".png" || fileExtendtion == ".psd" || fileExtendtion == "asv2" || fileExtendtion == "lasv"))
        {
            textureImporter.SetPlatformTextureSettings("Android", maxSize, format);
            textureImporter.SetPlatformTextureSettings("iPhone", maxSize, format);
        }
    }

    public static void CheckTextureSize(TextureImporter textureImporter, string assetPath)
    {
        GetTextureSize(textureImporter, out int width, out int height);
        var maxWidthHeight = Mathf.Max(width, height);
        var maxPlatformSize = textureImporter.maxTextureSize;
        #if UNITY_ANDROID
        maxPlatformSize = textureImporter.GetPlatformTextureSettings("Android").maxTextureSize;
        #elif UNITY_IPHONE
        maxPlatformSize = textureImporter.GetPlatformTextureSettings("iPhone").maxTextureSize;
        #endif
        var maxSize = Mathf.Min(maxWidthHeight, maxPlatformSize);
        var scale = (float) maxWidthHeight / maxSize;
        int widthScale = Mathf.RoundToInt(width / scale);
        int heightScale = Mathf.RoundToInt(height / scale);
        Debug.Log("widthScale = " + widthScale + ", heightScale = " + heightScale + " scale = " + scale + " maxSize = " + maxSize + " maxPlatformSize = " + maxPlatformSize + " assetPath = " + assetPath);
        if (widthScale % 4 == 0 && heightScale % 4 == 0) return;
        var dataAssetPath = Path.GetDirectoryName(assetPath) + "/" + Path.GetFileNameWithoutExtension(assetPath) + ".spriteatlasv2";
        if (File.Exists(dataAssetPath)) return;
        SpriteImporterData.AddNotOptimizedFile(assetPath);
        Debug.Log("Anh sai kich thuoc width = " + width + ", height = " + height + " : " + assetPath);
    }

    public static void GetTextureSize(TextureImporter textureImporter, out int width, out int height)
    {
        if (textureImporter == null)
        {
            width = 0;
            height = 0;
            return;
        }
        object[] args = new object[2] { 0, 0 };
        MethodInfo mi = typeof(TextureImporter).GetMethod("GetWidthAndHeight", BindingFlags.NonPublic | BindingFlags.Instance);
        mi.Invoke(textureImporter, args);
        width = (int)args[0];
        height = (int)args[1];
    }
} 
#endif