using System.Collections;
using System.Collections.Generic;
using System.IO;
using UnityEditor;
using UnityEditor.U2D;
using UnityEngine;

public static class EditorExtension
{
    public static void SetMasterAtlas(string varantatlas, string masteratlas)
    {
        var masterAtlasAssetId = AssetDatabase.AssetPathToGUID(masteratlas);
        Debug.Log("master atlas id : " + masterAtlasAssetId);
        var text = File.ReadAllText(varantatlas);
        text = text.ReplaceLineWith("m_MasterAtlas", $"  m_MasterAtlas: {{fileID: 100100200, guid: {masterAtlasAssetId}, type: 3}}");
        File.WriteAllText(varantatlas, text);
    }
}
