# Accuracy Rating System

## Overview

The game now supports two rating systems:

1. **FC Rating System** (Original): Based on hit types (SFC, GFC, FC, SDCB, Clear)
2. **Accuracy Rating System** (New): Based on accuracy percentages (?, F, E, D, C, B, A, S, S+)

## Accuracy Rating Thresholds

The accuracy-based rating system works as follows:

- **?** → Accuracy < 20%
- **F** → Accuracy ≥ 40%
- **E** → Accuracy ≥ 50%
- **D** → Accuracy ≥ 60%
- **C** → Accuracy ≥ 70%
- **B** → Accuracy ≥ 80%
- **A** → Accuracy ≥ 90%
- **S** → Accuracy ≥ 95%
- **S+** → Accuracy ≥ 99%

## Implementation Details

### GameController.cs
- Added `GetAccuracyRating()` method that calculates rating based on accuracy percentage
- Updated `UpdateDataForGameResult()` to store accuracy rating in DataTransaction

### FCRatingIconController.cs
- Added support for accuracy rating icons alongside FC rating icons
- Added `defaultAccuracyRatingIcons` list for accuracy rating sprites
- Added `UpdateAccuracyRatingIcon()` method for direct accuracy rating updates
- Added `SetUseAccuracyRating()` method to toggle between rating systems

### GameUI.cs
- Added `useAccuracyRatingSystem` toggle to switch between rating systems
- Updated `UpdateScoreInfo()` to support both rating systems
- Added `SetUseAccuracyRatingSystem()` and `IsUsingAccuracyRatingSystem()` methods

### DataTransaction.cs
- Added `ratingAccuracy` field to store accuracy-based rating

## How to Use

### In Inspector (Unity Editor)

1. **FCRatingIconController Component:**
   - Set up `Default Accuracy Rating Icons` list with sprites for ?, F, E, D, C, B, A, S, S+
   - Each entry needs:
     - `ratingName`: The rating string (?, F, E, D, C, B, A, S, S+)
     - `iconSprite`: The sprite to display for this rating
     - `animationName`: Optional animation name
     - `useAnimation`: Whether to use animation

2. **GameUI Component:**
   - Toggle `Use Accuracy Rating System` checkbox to switch between systems

### In Code

```csharp
// Get current accuracy rating
string accuracyRating = GameStatic.ins.gameController.GetAccuracyRating();

// Switch to accuracy rating system
gameUI.SetUseAccuracyRatingSystem(true);

// Switch back to FC rating system
gameUI.SetUseAccuracyRatingSystem(false);

// Check current rating system
bool usingAccuracy = gameUI.IsUsingAccuracyRatingSystem();

// Directly update accuracy rating icon
fcRatingIconController.UpdateAccuracyRatingIcon("S+");

// Set rating system preference on controller
fcRatingIconController.SetUseAccuracyRating(true);
```

## Notes

- The accuracy calculation uses the existing `GetAccuracy()` method which considers weighted hit types
- Both rating systems can coexist - you can display FC rating in text and accuracy rating in icons
- The system automatically updates during gameplay when `UpdateScoreInfo()` is called
- Icons will be hidden if no sprite is configured for a particular rating
- The scale animation works with both rating systems
