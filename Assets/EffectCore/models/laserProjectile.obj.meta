fileFormatVersion: 2
guid: 6db9b63ba56ce374e8ba9d8ef2f5af9a
timeCreated: **********
licenseType: Store
ModelImporter:
  serializedVersion: 19
  fileIDToRecycleName:
    100000: laser_projectile
    100002: //RootNode
    100004: laser_projectile2
    100006: laser_projectile_laserInnerProjectile
    100008: laserOuterProjectile_laser_projectile
    400000: laser_projectile
    400002: //RootNode
    400004: laser_projectile2
    400006: laser_projectile_laserInnerProjectile
    400008: laserOuterProjectile_laser_projectile
    2300000: laser_projectile
    2300002: laser_projectile2
    2300004: laser_projectile_laserInnerProjectile
    2300006: laserOuterProjectile_laser_projectile
    3300000: laser_projectile
    3300002: laser_projectile2
    3300004: laser_projectile_laserInnerProjectile
    3300006: laserOuterProjectile_laser_projectile
    4300000: laser_projectile
    4300002: laser_projectile2
    4300004: laser_projectile_laserInnerProjectile
    4300006: laserOuterProjectile_laser_projectile
  materials:
    importMaterials: 0
    materialName: 0
    materialSearch: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    clipAnimations: []
    isReadable: 1
  meshes:
    lODScreenPercentages: []
    globalScale: 0.01
    meshCompression: 0
    addColliders: 0
    importBlendShapes: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 1
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
  importAnimation: 1
  copyAvatar: 0
  humanDescription:
    serializedVersion: 2
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    rootMotionBoneRotation: {x: 0, y: 0, z: 0, w: 1}
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  animationType: 0
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
